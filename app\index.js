(()=>{"use strict";var e,t={9371:(e,t,r)=>{var n,a,o,i,c,l=r(5072),s=r(7665),u=r(9904),f=r(2564),m=r(9270),p=r(7097),h=r(4977),d=r(7039),E=r(4536),y=r(5263),R=r(6913),g=r(7e3),v=r(4547),S=r(3676),b=r(9296),T=r(4461),_=wpmc_media_cleaner.prefix,w=wpmc_media_cleaner.domain,I=wpmc_media_cleaner.rest_url.replace(/\/+$/,""),A=wpmc_media_cleaner.api_url.replace(/\/+$/,""),O=wpmc_media_cleaner.plugin_url.replace(/\/+$/,""),N="1"===wpmc_media_cleaner.is_pro,x=N&&"1"===wpmc_media_cleaner.is_registered,D=wpmc_media_cleaner.rest_nonce,L=wpmc_media_cleaner.options,k="files"===L.method?L.filesystem_content:L.content,C="files"===L.method&&(null==L?void 0:L.media_library),P=r(197),M=r(9616);function F(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}var G=M.Ay.div(n||(n=F(["\n  color: white;\n  padding: 15px;\n  margin-bottom: -15px;\n\n  a {\n    color: #7dedff;\n    text-decoration: none;\n  }\n\n  p {\n    font-size: 15px;\n  }\n"]))),B=(0,M.Ay)(T.z)(a||(a=F(["\n\n  .neko-block-title {\n    display: none;\n  }\n\n  .plugin-desc {\n    display: flex;\n    flex-direction: column;\n    margin-left: 15px;\n  }\n\n  .neko-block-content {\n    display: flex;\n    padding: 15px;\n\n    h2 {\n      font-size: 18px;\n      margin: 0;\n\n      a {\n        text-decoration: none;\n      }\n    }\n\n    p {\n      margin: 0px;\n      margin-top: 10px;\n      font-size: 13px;\n      line-height: 1.5;\n    }\n\n    .plugin-actual-desc {\n      font-size: 13px;\n      font-weight: 500;\n    }\n  }\n"]))),U=M.Ay.img(o||(o=F(["\n  height: 125px;\n  width: auto;\n  border-radius: 10px;\n  background: lightgray;\n"]))),H=M.Ay.div(i||(i=F(["\n\n  margin: 15px;\n\n  .center {\n    background: white;\n    color: black;\n    border-radius: 10px;\n    padding: 10px;\n    max-width: 100%\n    overflow: none;\n\n    h2 {\n      font-size: 26px;\n    }\n\n    table {\n      width: 100%;\n\n      tr td:first-child {\n        width: 220px;\n        font-weight: bold;\n        color: #1e7cba;\n      }\n\n      * {\n        overflow-wrap: anywhere;\n      }\n    }\n  }\n\n  hr {\n    border-color: #1e7cba;\n  }\n"]))),j=M.Ay.ul(c||(c=F(["\n  margin-top: 10px;\n  background: rgb(0, 72, 88);\n  padding: 10px;\n  color: rgb(58, 212, 58);\n  max-height: 600px;\n  min-height: 200px;\n  display: block;\n  font-family: monospace;\n  font-size: 12px;\n  white-space: pre;\n  overflow-x: auto;\n  width: calc(100vw - 276px);\n  color: white;\n\n  .log-date {\n    color: var(--neko-yellow);\n    margin-left: 8px;\n  }\n\n  .log-type {\n    background: #0000004d;\n    padding: 2px 5px;\n    border-radius: 8px;\n    text-transform: uppercase;\n  }\n\n  .log-content {\n    display: block;\n  }\n\n  .log-warning .log-type {\n    background: var(--neko-yellow);\n    color: white;\n  }\n\n  .log-fatal .log-type {\n    background: var(--neko-red);\n    color: white;\n  }\n"])));function Y(e){return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Y(e)}function V(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */V=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof y?t:y,i=Object.create(o.prototype),c=new x(n||[]);return a(i,"_invoke",{value:I(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var m="suspendedStart",p="suspendedYield",h="executing",d="completed",E={};function y(){}function R(){}function g(){}var v={};s(v,i,(function(){return this}));var S=Object.getPrototypeOf,b=S&&S(S(D([])));b&&b!==r&&n.call(b,i)&&(v=b);var T=g.prototype=y.prototype=Object.create(v);function _(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(a,o,i,c){var l=f(e[a],e,o);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==Y(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(u).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function I(t,r,n){var a=m;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===d){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=A(c,n);if(l){if(l===E)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var s=f(t,r,n);if("normal"===s.type){if(a=n.done?d:p,s.arg===E)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=d,n.method="throw",n.arg=s.arg)}}}function A(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),E;var o=f(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,E;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,E):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,E)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(Y(t)+" is not iterable")}return R.prototype=g,a(T,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:R,configurable:!0}),R.displayName=s(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===R||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,s(e,l,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},_(w.prototype),s(w.prototype,c,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new w(u(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},_(T),s(T,l,"Generator"),s(T,i,(function(){return this})),s(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,E):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),E},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),E}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),E}},t}function W(e){return function(e){if(Array.isArray(e))return J(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||X(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function z(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function q(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){z(o,n,a,i,c,"next",e)}function c(e){z(o,n,a,i,c,"throw",e)}i(void 0)}))}}function K(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,s=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return c}}(e,t)||X(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function X(e,t){if(e){if("string"==typeof e)return J(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?J(e,t):void 0}}function J(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var $=wp.element,Q=$.useState,Z=$.useEffect,ee="".concat(I,"/meow-common/v1"),te=function(e){var t=e.request,r=e.title,n=e.max,a=K(Q(!1),2),o=a[0],i=a[1],c=K(Q([]),2),l=c[0],s=c[1],f=l.length>0?l.reduce((function(e,t){return e+t})):0,m=l.length>0?Math.ceil(f/l.length):0,p=!l.length&&o;Z((function(){o&&setTimeout(q(V().mark((function e(){var r,n,a;return V().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=(new Date).getTime(),e.next=3,(0,u.IU)("".concat(ee,"/").concat(t),{method:"POST",nonce:D});case 3:n=(new Date).getTime(),a=n-r,s((function(e){return[].concat(W(e),[a])}));case 6:case"end":return e.stop()}}),e)}))),1e3)}),[l]);return React.createElement(G,{style:{width:200,textAlign:"center"}},React.createElement(h.s,{h2:!0,style:{color:"white"}},r),React.createElement(P.X,{size:200,value:p?n:m,max:n},React.createElement("span",{style:{fontSize:20}},p?"START":m+" ms"),React.createElement("span",{style:{fontSize:12}},p?"YOUR ENGINE":l.length+" requests")),React.createElement(b.M,{style:{width:"100%",marginTop:10},color:o?"#cc3627":"#ccb027",onClick:function(){o||s([]),i(!o)}},o?"Stop":"Start"))};function re(e){return re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},re(e)}function ne(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,s=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ae(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ae(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ae(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function oe(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */oe=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof y?t:y,i=Object.create(o.prototype),c=new x(n||[]);return a(i,"_invoke",{value:I(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var m="suspendedStart",p="suspendedYield",h="executing",d="completed",E={};function y(){}function R(){}function g(){}var v={};s(v,i,(function(){return this}));var S=Object.getPrototypeOf,b=S&&S(S(D([])));b&&b!==r&&n.call(b,i)&&(v=b);var T=g.prototype=y.prototype=Object.create(v);function _(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(a,o,i,c){var l=f(e[a],e,o);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==re(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(u).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function I(t,r,n){var a=m;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===d){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=A(c,n);if(l){if(l===E)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var s=f(t,r,n);if("normal"===s.type){if(a=n.done?d:p,s.arg===E)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=d,n.method="throw",n.arg=s.arg)}}}function A(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),E;var o=f(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,E;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,E):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,E)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(re(t)+" is not iterable")}return R.prototype=g,a(T,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:R,configurable:!0}),R.displayName=s(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===R||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,s(e,l,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},_(w.prototype),s(w.prototype,c,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new w(u(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},_(T),s(T,l,"Generator"),s(T,i,(function(){return this})),s(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,E):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),E},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),E}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),E}},t}function ie(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function ce(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){ie(o,n,a,i,c,"next",e)}function c(e){ie(o,n,a,i,c,"throw",e)}i(void 0)}))}}var le=wp.element,se=le.useState,ue=le.useEffect;A&&I&&O||console.error("[@common/dashboard] apiUrl, restUrl and pluginUrl are mandatory.");var fe="".concat(I,"/meow-common/v1"),me=React.createElement(G,null,React.createElement(h.s,{p:!0,style:{lineHeight:"1.5",margin:"0 0 15px 0"}},"Hi! ☀️ Meow Apps isn't your typical plugin suite—it's a passion project led by me, ",React.createElement("a",{target:"_blank",href:"https://jordymeow.com"},"Jordy Meow"),", and a stellar team! 💕 Based in ",React.createElement("a",{target:"_blank",href:"https://offbeatjapan.org"},"Japan"),", we're all about making your WordPress experience smoother and speedier. Our plugins are all about boosting your site's performance and user-friendliness. Ready to level up your WordPress game? Check out ",React.createElement("a",{href:"http://meowapps.com",target:"_blank"},"Meow Apps")," and let's make magic happen! 🌴🙀")),pe=React.createElement(G,null,React.createElement(h.s,{p:!0},"The ",React.createElement("b",null,"Empty Request Time")," measures your installation's basic performance by showing the average time needed to process an empty request on your server. To see how disabling plugins affects the results, turn some off and run the test again. Aim for a time under 2,000 ms, but ideally, keep it below 500 ms. The ",React.createElement("b",null,"File Operation Time")," creates a temporary 10MB file each time it runs. ",React.createElement("b",null,"The SQL Request Time")," calculates the total number of posts. This process should be quick and have a similar duration to the Empty Request Time.")),he=React.createElement(G,null,React.createElement(h.s,{p:!0},"Maintain a streamlined WordPress setup by using essential plugins and a dependable hosting provider. Refrain from self-hosting unless you're an expert. Go further by reading our tutorials:",React.createElement("ul",null,React.createElement("li",null,"⭐️ ",React.createElement("a",{href:"https://meowapps.com/tutorial-improve-seo-wordpress/",target:"_blank"},"SEO Checklist & Optimization")),React.createElement("li",null,"⭐️ ",React.createElement("a",{href:"https://meowapps.com/tutorial-faster-wordpress-optimize/",target:"_blank"},"Optimize your WordPress Speed")),React.createElement("li",null,"⭐️ ",React.createElement("a",{href:"https://meowapps.com/tutorial-optimize-images-wordpress/",target:"_blank"},"Optimize Images (CDN, and so on)")),React.createElement("li",null,"⭐️ ",React.createElement("a",{href:"https://meowapps.com/tutorial-hosting-service-wordpress/",target:"_blank"},"The Best Hosting Services for WordPress"))))),de=function(){var e=ce(oe().mark((function e(){var t;return oe().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,u.IU)("".concat(fe,"/all_settings/"),{method:"POST",nonce:D});case 2:return t=e.sent,e.abrupt("return",t.data);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ee=function(){var e=ce(oe().mark((function e(t){var r,n,a;return oe().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.value,n=t.id,e.next=3,(0,u.IU)("".concat(fe,"/update_option"),{method:"POST",nonce:D,json:{name:n,value:r}});case 3:return a=e.sent,e.abrupt("return",a);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ye=function(){var e=ce(oe().mark((function e(){var t;return oe().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,u.IU)("".concat(fe,"/error_logs"),{method:"POST",nonce:D});case 2:return t=e.sent,e.abrupt("return",t.data.reverse());case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Re=function(){var e=(0,s.jE)(),t=ne(se(!1),2),r=t[0],n=t[1],a=ne(se(!1),2),o=a[0],i=(a[1],ne(se(""),2)),c=i[0],l=i[1],u=(0,m.I)({queryKey:["all_settings"],queryFn:de}),f=u.data,_=u.error,w=(u.isLoading,(0,p.n)({mutationFn:Ee,onSuccess:function(){e.invalidateQueries(["all_settings"])}}).mutate),I=(0,p.n)({mutationFn:ye}),A=I.mutate,O=I.data,N=void 0===O?[]:O,x=null==f?void 0:f.meowapps_hide_meowapps,D=null==f?void 0:f.force_sslverify;ue((function(){_&&!r&&(n(!0),console.error("Error from useQuery",_.message))}),[_]),ue((function(){var e=document.getElementById("meow-common-phpinfo");l(e.innerHTML)}),[]);var L=function(e,t){w({value:e,id:t})},k=React.createElement(d.d,{title:"Main Menu"},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"meowapps_hide_meowapps",label:"Hide (Not Recommended)",description:React.createElement(h.s,{p:!0},"This will hide the Meow Apps Menu (on the left side) and everything it contains. You can re-enable it through though an option that will be added in Settings → General."),value:"1",disabled:o,checked:x,onChange:L}))),C=React.createElement(d.d,{title:"SSL Verify"},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"force_sslverify",label:"Force (Not Recommended)",description:React.createElement(h.s,{p:!0},"This will enforce the usage of SSL when checking the license or updating the plugin."),value:"1",disabled:o,checked:D,onChange:L})));return React.createElement(R.z,{showRestError:r},React.createElement(g.n,{title:"The Dashboard"}),React.createElement(v.N,null,React.createElement(v.Y,{full:!0},React.createElement(S._,{keepTabOnReload:!0},React.createElement(S.V,{title:"Meow Apps"},me,React.createElement(v.N,null,React.createElement(v.Y,{minimal:!0},React.createElement(B,{title:"AI Engine",className:"primary"},React.createElement(U,{src:"https://ps.w.org/ai-engine/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/ai-engine/"},"AI Engine")),React.createElement("p",{className:"plugin-actual-desc"},"This is the ultimate AI plugin for WordPress. From a chatbot adapted to your needs to an AI that can write your content for you, API, REST, and more."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/ai-engine/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/ai-engine/"},"Meow Apps"))))),React.createElement(B,{title:"Media Cleaner",className:"primary"},React.createElement(U,{src:"https://ps.w.org/media-cleaner/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/media-cleaner/"},"Media Cleaner")),React.createElement("p",{className:"plugin-actual-desc"},"Is your Media Library bloated, your database heavy, and your website running slow? Media Cleaner will clean your Media Library from the media entries (and files) which aren't used in your website, as well as broken entries."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/media-cleaner/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/media-cleaner/"},"Meow Apps"))))),React.createElement(B,{title:"Database Cleaner",className:"primary"},React.createElement(U,{src:"https://ps.w.org/database-cleaner/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/database-cleaner/"},"Database Cleaner")),React.createElement("p",{className:"plugin-actual-desc"},"Not only does Database Cleaner have a user-friendly UI, but it's also equipped to handle large DBs, giving it an edge over other plugins. It's a must-have for any WordPress site."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/database-cleaner/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/database-cleaner/"},"Meow Apps"))))),React.createElement(B,{title:"Media File Renamer",className:"primary"},React.createElement(U,{src:"https://ps.w.org/media-file-renamer/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/media-file-renamer/"},"Media File Renamer")),React.createElement("p",{className:"plugin-actual-desc"},"Rename and move files directly from the dashboard, manually, automatically or via AI, either individually or in bulk. It's the best way to rename your files."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/media-file-renamer/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/media-file-renamer/"},"Meow Apps"))))),React.createElement(B,{title:"Social Engine",className:"primary"},React.createElement(U,{src:"https://ps.w.org/social-engine/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/social-engine/"},"Social Engine")),React.createElement("p",{className:"plugin-actual-desc"},'Effortlessly schedule and automate the perfect posts for all your networks. Unlimited capabilities and infinite className="plugin-actual-desc" extensibility, for free!'),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/social-engine/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/social-engine/"},"Meow Apps"))))),React.createElement(B,{title:"Meow Analytics",className:"primary"},React.createElement(U,{src:"https://ps.w.org/meow-analytics/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/meow-analytics/"},"Meow Analytics")),React.createElement("p",{className:"plugin-actual-desc"},"Google Analytics for your website. Simple and fast."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/meow-analytics/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/meow-analytics/"},"Meow Apps")))))),React.createElement(v.Y,{minimal:!0},React.createElement(B,{title:"Contact Form Block",className:"primary"},React.createElement(U,{src:"https://ps.w.org/seo-engine/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/seo-engine/"},"SEO Engine")),React.createElement("p",{className:"plugin-actual-desc"},"Optimize your content for SEO and for the AI world, with AI assistants... while keeping everything simple and fast, as it should be! ✌️"),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/seo-engine/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/seo-engine/"},"Meow Apps"))))),React.createElement(B,{title:"Meow Gallery",className:"primary"},React.createElement(U,{src:"https://ps.w.org/meow-gallery/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/meow-gallery/"},"Meow Gallery")),React.createElement("p",{className:"plugin-actual-desc"},"Fast and beautiful galleries with many layouts. Forget the heavy and slow plugins, use the Meow Gallery for a better experience! 💕"),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/meow-gallery/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/meow-gallery/"},"Meow Apps"))))),React.createElement(B,{title:"Meow Lightbox",className:"primary"},React.createElement(U,{src:"https://ps.w.org/meow-lightbox/assets/icon-256x256.gif"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/meow-lightbox/"},"Meow Lightbox")),React.createElement("p",{className:"plugin-actual-desc"},"Sleek and performant lightbox with EXIF support."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/meow-lightbox/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/meow-lightbox/"},"Meow Apps"))))),React.createElement(B,{title:"Perfect Images (Retina)",className:"primary"},React.createElement(U,{src:"https://ps.w.org/wp-retina-2x/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/wp-retina-2x/"},"Perfect Images")),React.createElement("p",{className:"plugin-actual-desc"},"Manage, Optimize, Replace your images with Perfect Images."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/wp-retina-2x/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/wp-retina-2x/"},"Meow Apps"))))),React.createElement(B,{title:"Photo Engine",className:"primary"},React.createElement(U,{src:"https://ps.w.org/wplr-sync/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/wplr-sync/"},"Photo Engine")),React.createElement("p",{className:"plugin-actual-desc"},"Organize your photos in folders and collections. Synchronize with Lightroom. Simplify and speed up your workflow."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/wplr-sync/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/wplr-sync/"},"Meow Apps"))))),React.createElement(B,{title:"Contact Form Block",className:"primary"},React.createElement(U,{src:"https://ps.w.org/contact-form-block/assets/icon-256x256.png"}),React.createElement("div",{className:"plugin-desc"},React.createElement("h2",null,React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/contact-form-block/"},"Contact Form Block")),React.createElement("p",{className:"plugin-actual-desc"},"Need a very simple but straightforward contact form? This is the one you need. It's fast, simple, and efficient."),React.createElement("p",null,React.createElement("div",null,"Free Version: ",React.createElement("a",{target:"_blank",href:"https://wordpress.org/plugins/contact-form-block/"},"WordPress.org")),React.createElement("div",null,"Pro Version: ",React.createElement("a",{target:"_blank",href:"https://meowapps.com/contact-form-block/"},"Meow Apps")))))))),React.createElement(S.V,{title:"Performance"},pe,React.createElement("div",{style:{display:"flex",justifyContent:"space-around",marginBottom:25}},React.createElement(te,{title:"Empty Request Time",request:"empty_request",max:2500}),React.createElement(te,{title:"File Operation Time",request:"file_operation",max:2600}),React.createElement(te,{title:"SQL Request Time",request:"sql_request",max:2800})),he),React.createElement(S.V,{title:"PHP Info"},React.createElement(H,{dangerouslySetInnerHTML:{__html:c}})),React.createElement(S.V,{title:"PHP Error Logs"},React.createElement(G,null,React.createElement(b.M,{style:{marginBottom:10},color:"#ccb027",onClick:function(){A()},disabled:o,isBusy:"isLoadingErrorLogs"===o},"Load PHP Error Logs"),React.createElement(j,null,N.map((function(e){return React.createElement("li",{class:"log-".concat(e.type)},React.createElement("span",{class:"log-type"},e.type),React.createElement("span",{class:"log-date"},e.date),React.createElement("span",{class:"log-content"},e.content))}))),React.createElement(h.s,{p:!0},"If you don't see any errors, your host might not allow remote access to PHP error logs. Contact them for assistance, or look in your hosting control panel."))),React.createElement(S.V,{title:"Settings"},React.createElement(T.z,{title:"Settings",className:"primary"},k,C))))))},ge=r(8696),ve=r(9794),Se=r(1543),be=r(7213);function Te(e){return Te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Te(e)}function _e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_e=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof y?t:y,i=Object.create(o.prototype),c=new x(n||[]);return a(i,"_invoke",{value:I(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var m="suspendedStart",p="suspendedYield",h="executing",d="completed",E={};function y(){}function R(){}function g(){}var v={};s(v,i,(function(){return this}));var S=Object.getPrototypeOf,b=S&&S(S(D([])));b&&b!==r&&n.call(b,i)&&(v=b);var T=g.prototype=y.prototype=Object.create(v);function _(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(a,o,i,c){var l=f(e[a],e,o);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==Te(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(u).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function I(t,r,n){var a=m;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===d){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=A(c,n);if(l){if(l===E)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var s=f(t,r,n);if("normal"===s.type){if(a=n.done?d:p,s.arg===E)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=d,n.method="throw",n.arg=s.arg)}}}function A(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),E;var o=f(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,E;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,E):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,E)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(Te(t)+" is not iterable")}return R.prototype=g,a(T,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:R,configurable:!0}),R.displayName=s(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===R||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,s(e,l,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},_(w.prototype),s(w.prototype,c,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new w(u(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},_(T),s(T,l,"Generator"),s(T,i,(function(){return this})),s(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,E):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),E},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),E}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),E}},t}function we(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function Ie(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){we(o,n,a,i,c,"next",e)}function c(e){we(o,n,a,i,c,"throw",e)}i(void 0)}))}}function Ae(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,s=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Oe(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Oe(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Oe(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var Ne,xe=wp.element,De=xe.useState,Le=xe.useEffect,ke="".concat(I,"/meow-licenser/").concat(_,"/v1"),Ce=function(){var e=Ae(De(!1),2),t=e[0],r=e[1],n=Ae(De(!1),2),a=n[0],o=n[1],i=Ae(De(null),2),c=i[0],l=i[1],s=Ae(De(null),2),f=s[0],m=s[1],p=Ae(De(""),2),d=p[0],E=p[1],y=x&&(!f||"valid"!==f.license),R=function(){var e=Ie(_e().mark((function e(){var t;return _e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(N){e.next=2;break}return e.abrupt("return");case 2:return r(!0),e.prev=3,e.next=6,(0,u.IU)("".concat(ke,"/get_license"),{method:"POST",nonce:D});case 6:t=e.sent,m(t.data),t.data.key&&E(t.data.key),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(3),alert("Error while checking the license. Check your console for more information."),console.error(e.t0);case 15:r(!1);case 16:case"end":return e.stop()}}),e,null,[[3,11]])})));return function(){return e.apply(this,arguments)}}(),g=function(){var e=Ie(_e().mark((function e(){return _e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r(!0),e.prev=1,e.next=4,(0,u.IU)("".concat(ke,"/set_license"),{method:"POST",nonce:D,json:{serialKey:null}});case 4:e.sent.success&&(E(""),m(null),l("licenseRemoved")),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),alert("Error while removing the license. Check your console for more information."),console.error(e.t0);case 12:r(!1);case 13:case"end":return e.stop()}}),e,null,[[1,8]])})));return function(){return e.apply(this,arguments)}}(),v=function(){var e=Ie(_e().mark((function e(){var t;return _e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r(!0),e.prev=1,e.next=4,(0,u.IU)("".concat(ke,"/set_license"),{method:"POST",nonce:D,json:{serialKey:d,override:!0}});case 4:(t=e.sent).success&&(m(t.data),t.data&&!t.data.issue&&l("licenseAdded")),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),alert("Error while forcing the license. Check your console for more information."),console.error(e.t0);case 12:r(!1);case 13:case"end":return e.stop()}}),e,null,[[1,8]])})));return function(){return e.apply(this,arguments)}}(),S=function(){var e=Ie(_e().mark((function e(){var t;return _e().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("MEOW_OVERRIDE"!==d){e.next=5;break}return o(!0),m(null),E(""),e.abrupt("return");case 5:return r(!0),e.prev=6,e.next=9,(0,u.IU)("".concat(ke,"/set_license"),{method:"POST",nonce:D,json:{serialKey:d}});case 9:(t=e.sent).success&&(m(t.data),t.data&&!t.data.issue&&l("licenseAdded")),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(6),alert("Error while validating the license. Check your console for more information."),console.error(e.t0);case 17:r(!1);case 18:case"end":return e.stop()}}),e,null,[[6,13]])})));return function(){return e.apply(this,arguments)}}();Le((function(){R()}),[]);var _=y?"Forced License":x?"Enabled":"Disabled",w=y||f&&"valid"===f.license,I="Your license is active. Thanks a lot for your support :)";y&&(I="This license has been force-enabled for you.",f&&f.check_url&&(I=React.createElement(React.Fragment,null,React.createElement("span",null,I),React.createElement("br",null),React.createElement("small",null,"To check your license status, please click ",React.createElement("a",{target:"_blank",href:f.check_url+"&cache="+642e3*Math.random(),rel:"noreferrer"},"here"),".")))),w||(f?"no_activations_left"===f.issue?I=React.createElement("span",null,"There are no activations left for this license. You can visit your account at ",React.createElement("a",{target:"_blank",rel:"noreferrer",href:"https://meowapps.com"},"Meow Apps"),", unregister a site, and click on ",React.createElement("i",null,"Retry to validate"),"."):"expired"===f.issue?I=React.createElement("span",null,"Your license has expired. You can get another license or renew the current one by visiting your account at ",React.createElement("a",{target:"_blank",rel:"noreferrer",href:"https://meowapps.com"},"Meow Apps"),"."):"missing"===f.issue?I="This license does not exist.":"disabled"===f.issue?I="This license has been disabled.":"item_name_mismatch"===f.issue?I="This license seems to be for a different plugin... isn't it? :)":"forced"===f.issue?I="ABC":(I=React.createElement("span",null,"There is an unknown error related to the system or this serial key. Really sorry about this! Make sure your security plugins and systems are off temporarily. If you are still experiencing an issue, please ",React.createElement("a",{target:"_blank",rel:"noreferrer",href:"https://meowapps.com/contact/"},"contact us"),"."),console.error({license:f})):I="Unknown error :(");var A=React.createElement(T.z,{title:"Pro Version (Not Installed)",className:"primary"},"You will find more information about the Pro Version ",React.createElement("a",{target:"_blank",rel:"noreferrer",href:"https://meowapps.com"},"here"),". If you actually bought the Pro Version already, please remove the current plugin and download the Pro Version from your account at ",React.createElement("a",{target:"_blank",rel:"noreferrer",href:"https://meowapps.com/"},"Meow Apps"),"."),O=React.createElement(T.z,{title:"Pro Version (".concat(_,")"),busy:t,className:"primary"},!y&&!(f&&f.key===d)&&React.createElement(React.Fragment,null,React.createElement("div",{style:{marginBottom:10}},"License Key:"),React.createElement(ge.A,{id:"mfrh_pro_serial",name:"mfrh_pro_serial",disabled:t,value:d,onChange:function(e){return E(e)},placeholder:"Type your license key..."}),React.createElement(h.s,{p:!0},"Insert your serial key above. If you don't have one yet, you can get one ",React.createElement("a",{href:"https://meowapps.com"},"here"),". If there was an error during the validation, try the ",React.createElement("i",null,"Retry")," to ",React.createElement("i",null,"validate")," button.")),f&&!w&&React.createElement(be.X,{variant:"danger"},I),(y||f)&&w&&React.createElement(be.X,{variant:"success"},I),React.createElement("div",{style:{marginTop:15,display:"flex",justifyContent:"end"}},f&&!w&&React.createElement(b.M,{className:"secondary",disabled:t||!d,onClick:S},"Retry to validate"),f&&f.key===d&&React.createElement(b.M,{className:"secondary",disabled:t||!d,onClick:g},"Remove License"),React.createElement(b.M,{disabled:t||!d||f&&f.key===d,onClick:S},"Validate License"),a&&!w&&React.createElement(b.M,{disabled:t||!d||f&&f.key===d,onClick:v,className:"danger"},"Force License")),React.createElement(ve.n,{isOpen:"licenseAdded"===c,title:"Thank you :)",content:"The Pro features have been enabled. This page should be now reloaded.",okButton:{label:"Reload",onClick:function(){return location.reload()}}}),React.createElement(ve.n,{isOpen:"licenseRemoved"===c,title:"Goodbye :(",content:"The Pro features have been disabled. This page should be now reloaded.",okButton:{label:"Reload",onClick:function(){return location.reload()}}}));return N?O:A};function Pe(e){return Pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pe(e)}function Me(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=Pe(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=Pe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Pe(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Fe=wp.i18n.__,Ge={};Ge.COMMON={PLUGIN_NAME:Fe("Media Cleaner","media-cleaner"),TUTORIAL:Fe("Tutorial","media-cleaner"),SETTINGS:Fe("Settings","media-cleaner"),DASHBOARD:Fe("Dashboard","media-cleaner"),MEDIA_LIBRARY:Fe("Media Library","media-cleaner"),FILESYSTEM:Fe("Filesystem","media-cleaner"),BY_JORDY_MEOW:Fe("By Jordy Meow","media-cleaner"),MODAL_DOES_NOT_EXIST:Fe("The modal {0} doesn't exist.","media-cleaner")},Ge.ISSUE_CODES={NO_CONTENT:Fe("Not found in content","media-cleaner"),ORPHAN_MEDIA:Fe("Not attached file","media-cleaner"),ORPHAN_FILE:Fe("Not in library","media-cleaner"),ORPHAN_RETINA:Fe("Orphan @2x","media-cleaner"),ORPHAN_WEBP:Fe("Orphan WebP","media-cleaner")},Ge.DASHBOARD=(Me(Me(Me(Me(Me(Me(Me(Me(Me(Me(Ne={STEP:Fe("STEP","media-cleaner"),SCAN:Fe("Scan","media-cleaner"),PICK:Fe("Pick"),RESET:Fe("Reset","media-cleaner"),RESET_REFERENCES:Fe("Reset References","media-cleaner"),RESET_ISSUES:Fe("Reset Issues","media-cleaner"),EXTRACT_REFERENCES:Fe("Extract References","media-cleaner"),EXTRACT_REFERENCES_FOR_POST_ID:Fe("Extract References for Post ID","media-cleaner"),MATCH_WITH_REFERENCES:Fe("Match with References","media-cleaner"),FROM_FOLDER:Fe("From <strong>{0}</strong>","media-cleaner"),PICK_FOLDER:Fe("Pick Folder","media-cleaner"),RESET_FOLDER:Fe("Reset Folder","media-cleaner"),RESET_FOLDERS:Fe("Reset Folders","media-cleaner"),RE_ATTACH_MEDIA:Fe("Re-Attach Media","media-cleaner"),DELETE_ALL:Fe("Delete All","media-cleaner"),DELETE_STRONG_ALL:Fe("Delete <strong>All</strong>","media-cleaner"),RECOVER_ALL:Fe("Recover <strong>All</strong>","media-cleaner"),EMPTY_TRASH:Fe("Empty Trash","media-cleaner"),INFO_MESSAGE:Fe("There are <b>{0} issue{1}</b> with your files, accounting for <b>{2} MB</b>. Your trash contains <b>{3} MB</b>.","media-cleaner"),ISSUES:Fe("Issues","media-cleaner"),IGNORED:Fe("Ignored","media-cleaner"),TRASH:Fe("Trash","media-cleaner"),REFERENCES:Fe("Found In Use Medias","media-cleaner"),ACTION:Fe("Action","media-cleaner"),REPAIR:Fe("Repair","media-cleaner"),EMPTY_MESSAGE:Fe("It's all clean & nice here! 👍<br />Why not trying to give a boost to your database with <a target='_blank' href='https://wordpress.org/plugins/database-cleaner/'>Database Cleaner</a>? 😌","media-cleaner"),ITEMS_PAGE:Fe("items / page","media-cleaner"),DELETE_ALL_CONTENT:Fe("<span>If you continue, <b>{0} issue{1} will be deleted</b>. Make absolutely sure you have a backup of your site.</span>","media-cleaner"),TRASH_ALL_CONTENT:Fe("<span>If you continue, <b>the trash will be entirely emptied</b>. Make absolutely sure you have a backup of your site.</span>","media-cleaner"),FORCE_TRASH_ALL_CONTENT:Fe("It seems like the trash could not be emptied. Do you want to force clean the trash? This will permanently delete all the trashed files and their database entries with no security check. Make absolutely sure you have a backup of your site, or that you are sure about this action.","media-cleaner")},"RECOVER_ALL",Fe("Recover All","media-cleaner")),"RECOVER_ALL_CONTENT",Fe("Are you sure you really want to recover all the files from the trash?","media-cleaner")),"ERROR",Fe("Error","media-cleaner")),"RETRY",Fe("Retry","media-cleaner")),"AUTO_RETRY",Fe("AUTO RETRY","media-cleaner")),"AUTO_RETRY_DESCRIPTION",Fe("AUTO RETRY will retry the actions up to 10 times. At every retry, it will increase the delay (in order to avoid timeouts). Skipping errors is not recommended at all as it will result in a lot of false-positives. Check your browser console for more information.","media-cleaner")),"SKIP",Fe("Skip","media-cleaner")),"ALWAYS_SKIP",Fe("ALWAYS SKIP","media-cleaner")),"STOP",Fe("Stop","media-cleaner")),"FINISHED",Fe("Finished","media-cleaner")),Me(Me(Me(Me(Me(Me(Me(Me(Me(Me(Ne,"CLOSE",Fe("Close","media-cleaner")),"OPEN_IMAGE",Fe("Open Image","media-cleaner")),"ORIGIN",Fe("Origin","media-cleaner")),"TITLE_PATH",Fe("Title & Path","media-cleaner")),"ISSUE",Fe("Issue","media-cleaner")),"SIZE",Fe("Size","media-cleaner")),"ID",Fe("ID","media-cleaner")),"THUMBNAIL",Fe("Thumbnail","media-cleaner")),"POST",Fe("Post","media-cleaner")),"MEDIA_ID",Fe("Media ID","media-cleaner")),Me(Me(Me(Me(Me(Me(Me(Me(Me(Me(Ne,"MEDIA_URL",Fe("Media URL","media-cleaner")),"ORIGIN_TYPE",Fe("Origin Type","media-cleaner")),"FINAL_MESSAGE",Fe("The process has finished, but {0} error(s) occurred.","media-cleaner")),"STATUS_GETTING_IDS",Fe("Getting IDs...","media-cleaner")),"STATUS_EMPTYING",Fe("Emptying...","media-cleaner")),"STATUS_EMPTYING_SUCCESS",Fe("🟢 Emptying success!","media-cleaner")),"STATUS_DELETING",Fe("Deleting...","media-cleaner")),"STATUS_RECOVERING",Fe("Recovering...","media-cleaner")),"STATUS_IGNORING",Fe("Ignoring...","media-cleaner")),"STATUS_UNDOING",Fe("Undoing...","media-cleaner")),Me(Me(Me(Me(Me(Me(Me(Me(Me(Me(Ne,"STATUS_RESETTING_ISSUES_AND_REFERENCES",Fe("Resetting issues and references...","media-cleaner")),"STATUS_RESETTING_ISSUES",Fe("Resetting issues...","media-cleaner")),"STATUS_RESETTING_REFERENCES",Fe("Resetting references...","media-cleaner")),"STATUS_EXTRACT_FROM_CONTENT",Fe("Extract from content...","media-cleaner")),"STATUS_EXTRACT_FROM_LIBRARY",Fe("Extract from library...","media-cleaner")),"STATUS_LISTING_MEDIA",Fe("Listing media...","media-cleaner")),"STATUS_LISTING_FILES",Fe("Listing files...","media-cleaner")),"STATUS_ERROR_UNKNOWN_METHOD",Fe("This method is not known!","media-cleaner")),"STATUS_MATCHING_WITH_CONTENT",Fe("Matching with content...","media-cleaner")),"STATUS_SCANNING_WITH_REPAIR_MODE",Fe("Scanning with repair mode...","media-cleaner")),Me(Me(Me(Me(Me(Me(Me(Me(Me(Ne,"AUTO_ATTACH_FEATURE_MESSAGE",Fe("Currently, the Auto-Attach feature is handled by Media File Renamer: <a target='_blank' href='https://wordpress.org/plugins/media-file-renamer/'>https://wordpress.org/plugins/media-file-renamer/</a>. It will use the references data.","media-cleaner")),"REFERENCE_COLUMN_FILTERS",{SHOW_ALL:Fe("Show All","media-cleaner"),ONLY_MEDIA_IDS:Fe("Only Media IDs","media-cleaner"),ONLY_MEDIA_URLS:Fe("Only Media URLs","media-cleaner")}),"REPAIR_MODE_DESCRIPTION",Fe("⚡In short, the 'Repair' button sends the file back to the Media Library.\n\n🗒️When you use the 'Repair' button, Media Cleaner will take action to resolve issues with your media files.\n\nFirst, it identifies any related items (child issues) that might be causing problems and removes them to prevent future complications.\n\nThen, it re-adds the main media file (the parent) back into your Media Library.\n\nBy doing this, the file is no longer seen as an isolated thumbnail or a 'lost' file; it becomes a fully recognized media item again, just like all the other content in your Media Library.","media-cleaner")),"IGNORE_SELECTED",Fe("Ignore&nbsp;<strong>{0}</strong>&nbsp;entries","media-cleaner")),"DELETE_SELECTED",Fe("Delete&nbsp;<strong>{0}</strong>&nbsp;entries","media-cleaner")),"REPAIR_SELECTED",Fe("Repair&nbsp;<strong>{0}</strong>&nbsp;entries","media-cleaner")),"UNDO_SELECTED",Fe("Undo&nbsp;<strong>{0}</strong>&nbsp;entries","media-cleaner")),"RECOVER_SELECTED",Fe("Recover&nbsp;<strong>{0}</strong>&nbsp;entries","media-cleaner")),"DELETE_PERMANENTLY_SELECTED",Fe("Delete Permanently&nbsp;<strong>{0}</strong>&nbsp;entries","media-cleaner"))),Ge.NOTIFICATIONS={GET_PRO_VERSION:Fe("Get the Pro Version","media-cleaner"),READ_TUTORIAL:Fe("Read the tutorial","media-cleaner"),SCAN_MEDIA_LIBRARY_FOR_BROKEN_ENTRIES:Fe("Media Cleaner will scan your Media Library for broken entries.","media-cleaner"),SCAN_MEDIA_LIBRARY_FOR_UNUSED_ENTRIES:Fe("Media Cleaner will scan your Media Library for entries which aren't used in your content.","media-cleaner"),SCAN_PHYSICAL_DIRS_FOR_UNREGISTERED_OR_UNUSED:Fe("Media Cleaner will scan your physical directories for files which are not registered in the Media Library or not used directly in your content.","media-cleaner"),SCAN_PHYSICAL_DIRS_FOR_UNREGISTERED:Fe("Media Cleaner will scan your physical directories for files which are not registered in the Media Library.","media-cleaner"),RETURN_ALL_FILES_FROM_PHYSICAL_DIRS:Fe("Media Cleaner will return all the files from your physical directories, without checking if the files are used or not.","media-cleaner"),WARNING_IMPORTANCE:Fe("<b><span style='color:red;'>This is important.</span> Backup your DB and your /uploads directory before using Media Cleaner.</b> The deleted files will be temporarily moved to the <i>uploads/wpmc-trash</i> directory. After testing your site, you can check the <i>Trash</i> in order to empty it or to recover your files. That said, you still must have a backup! If you don't know how, give a try to this: <a href='http://meow.click/blogvault' target='_blank'>BlogVault</a>.","media-cleaner"),WARNING_CONSIDERATION:Fe("<b><span style='color:red;'>Please be considerate.</span> Don't blame Media Cleaner if it deleted too many (or not enough) of your files.</b> WordPress being a very dynamic and pluggable system, it is impossible to predict all the situations in which your files are used. Please do not post a bad review because it broke your install; if you have a proper backup, there is no risk! You can disable this big warning in the options if you have the Pro Version. Read this warning twice. Media Cleaner is awesome and constantly gets better so I sincerely hope you will enjoy it. Thank you :)","media-cleaner"),FREE_VERSION_INSTRUNTION:Fe("You must <b>backup the uploads folder and DB</b> before using the Cleaner. If you don't know how, give a try to <a target='_blank' href='https://meow.click/blogvault'>BlogVault</a>. It is also highly recommanded to read the tutorial. Last but not least, check the <b>Pro Version</b>, as it adds many features and also supports the development. This message is only displayed in the free version. Thank you!","media-cleaner"),IMCOMPATIBLE_PLUGINS_NOTE:Fe("Important note about the following plugin(s) which you are using:","media-cleaner"),IMCOMPATIBLE_PLUGINS_MESSAGE:Fe("They require additional checks which are only implemented in <a target='_blank' href='//meowapps.com/plugin/media-cleaner'>Media Cleaner Pro</a>.","media-cleaner")},Ge.SETTINGS={CONTENT:Fe("Content","media-cleaner"),CHECK:Fe("Check","media-cleaner"),ENABLE:Fe("Enable","media-cleaner"),CONTENT_DESCRIPTION:Fe("Checks if the media entries are used by the content (Posts, Pages and other Post Types, Metadata, Widgets, etc). Pagebuilders are only supported in the Pro Version.","media-cleaner"),FILESYSTEM_CONTENT_DESCRIPTION:Fe("Checks if the files are used by the content (Posts, Pages and other Post Types, Metadata, Widgets, etc).","media-cleaner"),MEDIA_LIBRARY_DESCRIPTION:Fe("Checks if the files are properly registered in the Media Library.","media-cleaner"),LIVE_CONTENT:Fe("Live Content","media-cleaner"),LIVE_CONTENT_DESCRIPTION:Fe("The live version of the site will be also analyzed (as if a visitor was loading it). It increases the accuracy of the results but slows down the speed dramatically.","media-cleaner"),IMAGES_ONLY:Fe("Images Only","media-cleaner"),IMAGES_ONLY_DESCRIPTION:Fe("Restricts the scan to images. Nothing else will be scanned.","media-cleaner"),ATTACHED_IMAGES:Fe("Attached Images","media-cleaner"),ATTACHED_IMAGES_DESCRIPTION:Fe("If a media entry is attached (in the Media Library, it is the <b>Uploaded To</b> field), it will be considered as being used.","media-cleaner"),CONSIDER_AS_IN_USE:Fe("Consider as In Use","media-cleaner"),THUMBNAILS_ONLY:Fe("Thumbnails Only","media-cleaner"),THUMBNAILS_ONLY_DESCRIPTION:Fe("Restricts Media Cleaner to scan thumbnails. With WordPress, those filenames contain the resolution.","media-cleaner"),DIRECTORIES_FILTER:Fe("Directories Filter","media-cleaner"),FILES_FILTER:Fe("Files Filter","media-cleaner"),HIDE:Fe("Hide","media-cleaner"),YES:Fe("Yes","media-cleaner"),HIDE_THUMBNAILS:Fe("Thumbnails","media-cleaner"),HIDE_THUMBNAILS_DESCRIPTION:Fe("If you prefer not to see the thumbnails.","media-cleaner"),SKIP_TRASH:Fe("Skip Trash","media-cleaner"),SKIP_TRASH_DESCRIPTION:Fe("This will disable the trash and the restore related features.","media-cleaner"),WARNING_MESSAGE:Fe("Warning Message","media-cleaner"),WARNING_MESSAGE_DESCRIPTION:Fe("Have you read it twice? If yes, hide it :)","media-cleaner"),PLUGIN_DATA:Fe("Plugin Data","media-cleaner"),PLUGIN_DATA_DESCRIPTION:Fe("The database and all the options of the plugin will be removed on uninstall. This also includes the information about the plugin's trash.","media-cleaner"),DELETE_ALL:Fe("Delete all","media-cleaner"),MEDIAS_BUFFER:Fe("Medias Buffer","media-cleaner"),MEDIAS_BUFFER_DESCRIPTION:Fe("The number of media entries to read at a time. This is fast, so the value should be between 50 and 1000.","media-cleaner"),POSTS_BUFFER:Fe("Posts Buffer","media-cleaner"),POSTS_BUFFER_DESCRIPTION:Fe("The number of posts (and any other post types) to analyze at a time. This is the most intense part of the process. Recommended value is between 1 (slow server) and 20 (excellent server).","media-cleaner"),ANALYSIS_BUFFER:Fe("Analysis Buffer","media-cleaner"),ANALYSIS_BUFFER_DESCRIPTION:Fe("The number of media entries or files to analyze at a time. This is the main part of the process, but is is much faster than analyzing each post. Recommended value is between 20 (slow server) and 1000 (excellent server).","media-cleaner"),FILE_OPERATION_BUFFER:Fe("File Operation Buffer","media-cleaner"),FILE_OPERATION_BUFFER_DESCRIPTION:Fe("The number of media entries or files to delete at a time. This highly depends on your server, it is normally quite fast. Recommended value is between 5 (slow server) and 100 (excellent server).","media-cleaner"),DELAY:Fe("Delay (in ms)","media-cleaner"),DELAY_DESCRIPTION:Fe("Time to wait between each request (in milliseconds). The overall process is intensive so this gives the chance to your server to chill out a bit. A very good server doesn't need it, but a slow/shared hosting might even reject requests if they are too fast and frequent. Recommended value is actually 0, 100 for safety, 2000 or 5000 if your hosting is kind of cheap.","media-cleaner"),DISABLE_ANALYSIS:Fe("Disable Analysis","media-cleaner"),DISABLE_CLEANING:Fe("Disable Cleaning","media-cleaner"),OUTPUT_BUFFER_CLEANING:Fe("Output Buffer Cleaning","media-cleaner"),PHP_ERROR_LOGS:Fe("PHP Error Logs","media-cleaner"),PHP_ERROR_LOGS_DESCRIPTION:Fe("The logs will also be written in the PHP error logs.","media-cleaner"),OUTPUT_BUFFER_CLEANING_DESCRIPTION:Fe("Sometimes other plugins or themes might use the output buffer to display content thinking you are looking at the pages being scanned. By default the plugin will clean the output buffer to sanitize the server responses.","media-cleaner"),SHORTCODES:Fe("Shortcodes","media-cleaner"),SHORTCODES_DESCRIPTION:Fe("Resolving shortcodes increase accuracy, but makes the process slower and takes more memory.","media-cleaner"),LOGS:Fe("Logs","media-cleaner"),LOGS_DESCRIPTION:Fe("Simple logging that explains which actions has been run.","media-cleaner"),REFRESH_LOGS:Fe("Refresh Logs","media-cleaner"),CLEAR_LOGS:Fe("Clear Logs","media-cleaner"),RESET_OPTIONS:Fe("Reset Options","media-cleaner"),EXPORT_OPTIONS:Fe("Export Options","media-file-renamer"),IMPORT_OPTIONS:Fe("Import Options","media-file-renamer"),DONE:Fe("Done!","media-cleaner"),RESET_DB_CONTENT:Fe("The Media Cleaner's database has been deleted. It will be re-created automatically next time you visit the Media Cleaner Dashboard.","media-cleaner"),ACCESS_DASHBOARD:Fe("Access Media Cleaner Dashboard","media-cleaner"),DELETE_CLEANER_DB:Fe("Delete Cleaner DB","media-cleaner"),SCAN_DESCRIPTION:Fe("There are two kinds of scan. Usually, the <b>Media Library</b> should be scanned first for images which are not used (Content = Check). Then, the <b>Filesystem</b> should be scanned for images which aren't registered in the Media Library (Media Library = Check). Check the <a target='_blank' href='https://meowapps.com/media-cleaner/tutorial/'>tutorial</a> tutorial for more information. If you wish to clean your <b>Database</b>, try <a target='_blank' href='https://wordpress.org/plugins/database-cleaner/'>Database Cleaner</a>, you will love it! 💕","media-cleaner"),MEDIA_LIBRARY_SCAN:Fe("Media Library Scan","media-cleaner"),SCANNING:Fe("Scanning","media-cleaner"),EITHER_SCANNING_DESCRIPTION:Fe("If you would like Media Cleaner to analyze your Media Library for broken entries, uncheck everything above.","media-cleaner"),NEITHER_SCANNING_DESCRIPTION:Fe("<b>Since Content and Live Content are not checked, Media Cleaner will analyze your Media Library for <u>broken entries</u>.</b>","media-cleaner"),FILTERS:Fe("Filters","media-cleaner"),EXTRA_RULES:Fe("Extra Rules","media-cleaner"),FILESYSTEM_SCAN:Fe("Filesystem Scan","media-cleaner"),FILESYSTEM_SCANNING_DESCRIPTION:Fe("If none of the checks above are selected, you will get the list of all your files.","media-cleaner"),ADVANCED:Fe("Advanced","media-cleaner"),UI_LOGS:Fe("UI & Logs","media-cleaner"),ON_UNINSTALL:Fe("On Uninstall","media-cleaner"),FOR_ADVANCED_USERS_DEVELOPERS:Fe("For Advanced Users & Developers","media-cleaner"),ADVANCED_SETTINGS:Fe("Advanced Settings","media-cleaner"),LICENSE:Fe("License","media-cleaner"),LOGS_DEBUG:Fe("Logs & Debug","media-cleaner"),EXPERT_MODE:Fe("Expert Mode","media-cleaner"),ERROR_EXPORT_SETTINGS:Fe("Error while exporting settings. Please check your console.","media-cleaner"),SUCCESS_IMPORT_SETTINGS:Fe("Settings imported. The page will now reload to reflect the changes.","media-cleaner"),ERROR_IMPORT_SETTINGS:Fe("Error while importing settings. Please check your console.","media-cleaner")};const Be=Ge;function Ue(e){return Ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ue(e)}function He(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */He=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof y?t:y,i=Object.create(o.prototype),c=new x(n||[]);return a(i,"_invoke",{value:I(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var m="suspendedStart",p="suspendedYield",h="executing",d="completed",E={};function y(){}function R(){}function g(){}var v={};s(v,i,(function(){return this}));var S=Object.getPrototypeOf,b=S&&S(S(D([])));b&&b!==r&&n.call(b,i)&&(v=b);var T=g.prototype=y.prototype=Object.create(v);function _(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(a,o,i,c){var l=f(e[a],e,o);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==Ue(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(u).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function I(t,r,n){var a=m;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===d){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=A(c,n);if(l){if(l===E)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var s=f(t,r,n);if("normal"===s.type){if(a=n.done?d:p,s.arg===E)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=d,n.method="throw",n.arg=s.arg)}}}function A(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),E;var o=f(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,E;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,E):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,E)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(Ue(t)+" is not iterable")}return R.prototype=g,a(T,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:R,configurable:!0}),R.displayName=s(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===R||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,s(e,l,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},_(w.prototype),s(w.prototype,c,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new w(u(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},_(T),s(T,l,"Generator"),s(T,i,(function(){return this})),s(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,E):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),E},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),E}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),E}},t}function je(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ye(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?je(Object(r),!0).forEach((function(t){Ve(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):je(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ve(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=Ue(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=Ue(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ue(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function We(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function ze(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){We(o,n,a,i,c,"next",e)}function c(e){We(o,n,a,i,c,"throw",e)}i(void 0)}))}}function qe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,s=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Ke(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ke(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ke(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var Xe=wp.element.useState,Je={marginTop:10,background:"rgb(0, 72, 88)",padding:10,color:"rgb(58, 212, 58)",maxHeight:600,minHeight:200,display:"block",fontFamily:"monospace",fontSize:12,whiteSpace:"pre",overflowX:"auto",width:"calc(100vw - 276px)"};const $e=function(){var e=qe(Xe(L),2),t=e[0],r=e[1],n=qe(Xe(!1),2),a=n[0],o=n[1],i=qe(Xe(""),2),c=i[0],l=i[1],s=qe(Xe(),2),f=s[0],m=s[1],p=a,I=t.content,O=t.filesystem_content,k=t.media_library,C=t.live_content,P=t.debuglogs,M=t.images_only,F=t.attach_is_use,G=t.thumbnails_only,B=t.dirs_filter,U=t.files_filter,H=t.hide_thumbnails,j=t.hide_warning,Y=t.skip_trash,V=t.medias_buffer,W=t.posts_buffer,z=t.analysis_buffer,q=t.file_op_buffer,K=t.delay,X=t.shortcodes_disabled,J=t.output_buffer_cleaning_disabled,$=t.php_error_logs,Q=t.clean_uninstall,Z=t.expert_mode,ee=function(){var e=ze(He().mark((function e(n,a){var i,c;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=Ye(Ye({},t),{},Ve({},a,n)),o(!0),e.prev=2,e.next=5,(0,u.IU)("".concat(A,"/update_options"),{method:"POST",json:{options:i},nonce:D});case 5:(c=e.sent).success&&r(c.options),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),alert(e.t0.message);case 12:return e.prev=12,o(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[2,9,12,15]])})));return function(t,r){return e.apply(this,arguments)}}(),te=function(){var e=ze(He().mark((function e(t){var n;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o(!0),e.prev=1,e.next=4,(0,u.IU)("".concat(A,"/update_options"),{method:"POST",nonce:D,json:{options:t}});case 4:(n=e.sent).success||alert(n.message),r(n.options),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),alert(e.t0.message);case 12:return e.prev=12,o(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[1,9,12,15]])})));return function(t){return e.apply(this,arguments)}}(),re=function(){var e=ze(He().mark((function e(){var t;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o(!0),e.prev=1,e.next=4,(0,u.IU)("".concat(A,"/reset_options"),{nonce:D,method:"POST"});case 4:(t=e.sent).success&&r(t.options),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),e.t0.message&&alert(e.t0.message);case 11:return e.prev=11,o(!1),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}(),ne=function(){var e=ze(He().mark((function e(){return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o(!0),e.next=3,(0,u.IU)("".concat(A,"/reset_db"),{nonce:D,method:"POST"});case 3:m("resetDb"),o(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ae=function(){var e=ze(He().mark((function e(){var t;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o(!0),e.next=3,(0,u.IU)("".concat(A,"/refresh_logs"),{nonce:D,method:"POST"});case 3:(t=e.sent).success&&l(t.data),o(!1);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),oe=function(){var e=ze(He().mark((function e(){var t;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o(!0),e.next=3,(0,u.IU)("".concat(A,"/clear_logs"),{nonce:D,method:"POST"});case 3:(t=e.sent).success&&l(t.data),o(!1);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ie=function(){var e=ze(He().mark((function e(){var t;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,u.IU)("".concat(A,"/all_settings"),{method:"GET",nonce:D});case 2:return t=e.sent,e.abrupt("return",null==t?void 0:t.data);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ce=function(){var e=ze(He().mark((function e(){var t,r,n,a,i,c,l;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o(!0),e.prev=1,t=new Date,e.next=5,ie();case 5:r=e.sent,n={options:r},a="media-cleaner-".concat(t.getFullYear(),"-").concat(t.getMonth()+1,"-").concat(t.getDate(),".json"),i=new Blob([JSON.stringify(n)],{type:"application/json"}),c=URL.createObjectURL(i),(l=document.createElement("a")).href=c,l.setAttribute("download",a),l.click(),e.next=20;break;case 16:e.prev=16,e.t0=e.catch(1),alert(Be.SETTINGS.ERROR_EXPORT_SETTINGS),console.log(e.t0);case 20:return e.prev=20,o(!1),e.finish(20);case 23:case"end":return e.stop()}}),e,null,[[1,16,20,23]])})));return function(){return e.apply(this,arguments)}}(),le=function(){var e=ze(He().mark((function e(){var t;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:o(!0);try{(t=document.createElement("input")).type="file",t.accept="application/json",t.onchange=function(){var e=ze(He().mark((function e(t){var r,n;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.target.files[0]){e.next=3;break}return e.abrupt("return");case 3:(n=new FileReader).onload=function(){var e=ze(He().mark((function e(t){var r,n;return He().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=JSON.parse(t.target.result),n=r.options,e.next=4,te(n);case 4:alert(Be.SETTINGS.SUCCESS_IMPORT_SETTINGS),window.location.reload();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),n.readAsText(r);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),t.click()}catch(e){alert(Be.SETTINGS.ERROR_IMPORT_SETTINGS),console.log(e)}finally{o(!1)}case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),se=React.createElement(d.d,{title:Be.SETTINGS.CONTENT},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"content",label:Be.SETTINGS.CHECK,description:Be.SETTINGS.CONTENT_DESCRIPTION,value:"1",checked:I,onChange:ee}))),ue=React.createElement(d.d,{title:Be.SETTINGS.CONTENT},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"filesystem_content",label:Be.SETTINGS.CHECK,description:Be.SETTINGS.FILESYSTEM_CONTENT_DESCRIPTION,value:"1",checked:O,onChange:ee}))),fe=React.createElement(d.d,{title:Be.COMMON.MEDIA_LIBRARY},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"media_library",label:Be.SETTINGS.CHECK,description:Be.SETTINGS.MEDIA_LIBRARY_DESCRIPTION,value:"1",checked:k,onChange:ee}))),me=(d.d,Be.SETTINGS.LIVE_CONTENT,E.E,y.R,Be.SETTINGS.CHECK,Be.SETTINGS.LIVE_CONTENT_DESCRIPTION,React.createElement(d.d,{title:Be.SETTINGS.IMAGES_ONLY},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"images_only",label:Be.SETTINGS.ENABLE,description:Be.SETTINGS.IMAGES_ONLY_DESCRIPTION,value:"1",checked:M,onChange:ee})))),pe=React.createElement(d.d,{title:Be.SETTINGS.ATTACHED_IMAGES},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"attach_is_use",label:Be.SETTINGS.CONSIDER_AS_IN_USE,description:(0,u.FE)(Be.SETTINGS.ATTACHED_IMAGES_DESCRIPTION),value:"1",checked:F,onChange:ee}))),he=React.createElement(d.d,{title:Be.SETTINGS.THUMBNAILS_ONLY},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"thumbnails_only",label:Be.SETTINGS.ENABLE,description:Be.SETTINGS.THUMBNAILS_ONLY_DESCRIPTION,value:"1",checked:G,onChange:ee}))),de=React.createElement(d.d,{title:Be.SETTINGS.DIRECTORIES_FILTER},React.createElement(ge.A,{name:"dirs_filter",onReset:function(){ee("","dirs_filter")},value:B,placeholder:"/regex/",onEnter:ee,onBlur:ee})),Ee=React.createElement(d.d,{title:Be.SETTINGS.FILES_FILTER},React.createElement(ge.A,{name:"files_filter",onReset:function(){ee("","files_filter")},value:U,placeholder:"/regex/",onEnter:ee,onBlur:ee})),ye=React.createElement(d.d,{title:Be.SETTINGS.HIDE_THUMBNAILS},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"hide_thumbnails",label:Be.SETTINGS.HIDE,description:Be.SETTINGS.HIDE_THUMBNAILS_DESCRIPTION,value:"1",checked:H,onChange:ee}))),Re=React.createElement(d.d,{title:Be.SETTINGS.SKIP_TRASH},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"skip_trash",label:Be.SETTINGS.YES,description:Be.SETTINGS.SKIP_TRASH_DESCRIPTION,value:"1",checked:Y,onChange:ee}))),be=React.createElement(d.d,{title:Be.SETTINGS.WARNING_MESSAGE},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"hide_warning",label:Be.SETTINGS.HIDE,description:Be.SETTINGS.WARNING_MESSAGE_DESCRIPTION,requirePro:!x,value:"1",checked:j,onChange:ee}))),Te=React.createElement(d.d,{title:Be.SETTINGS.EXPERT_MODE},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"expert_mode",label:Be.SETTINGS.ENABLE,value:"1",checked:Z,onChange:ee}))),_e=React.createElement(d.d,{title:Be.SETTINGS.PLUGIN_DATA},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"clean_uninstall",label:Be.SETTINGS.DELETE_ALL,description:Be.SETTINGS.PLUGIN_DATA_DESCRIPTION,value:"1",checked:Q,onChange:ee}))),we=React.createElement(d.d,{title:Be.SETTINGS.MEDIAS_BUFFER},React.createElement(ge.A,{name:"medias_buffer",type:"number",value:V,min:"1",max:"1000",onEnter:ee,onBlur:ee,description:Be.SETTINGS.MEDIAS_BUFFER_DESCRIPTION})),Ie=React.createElement(d.d,{title:Be.SETTINGS.POSTS_BUFFER},React.createElement(ge.A,{name:"posts_buffer",type:"number",value:W,min:"1",max:"100",onEnter:ee,onBlur:ee,description:Be.SETTINGS.POSTS_BUFFER_DESCRIPTION})),Ae=React.createElement(d.d,{title:Be.SETTINGS.ANALYSIS_BUFFER},React.createElement(ge.A,{name:"analysis_buffer",type:"number",value:z,min:"1",max:"1000",onEnter:ee,onBlur:ee,description:Be.SETTINGS.ANALYSIS_BUFFER_DESCRIPTION})),Oe=React.createElement(d.d,{title:Be.SETTINGS.FILE_OPERATION_BUFFER},React.createElement(ge.A,{name:"file_op_buffer",type:"number",value:q,min:"1",max:"100",onEnter:ee,onBlur:ee,description:Be.SETTINGS.FILE_OPERATION_BUFFER_DESCRIPTION})),Ne=React.createElement(d.d,{title:Be.SETTINGS.DELAY},React.createElement(ge.A,{name:"delay",type:"number",value:K,min:"0",max:"20000",onEnter:ee,onBlur:ee,description:Be.SETTINGS.DELAY_DESCRIPTION})),xe=React.createElement(d.d,{title:Be.SETTINGS.SHORTCODES},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"shortcodes_disabled",label:Be.SETTINGS.DISABLE_ANALYSIS,description:Be.SETTINGS.SHORTCODES_DESCRIPTION,value:"1",checked:X,onChange:ee}))),De=React.createElement(d.d,{title:Be.SETTINGS.OUTPUT_BUFFER_CLEANING},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"output_buffer_cleaning_disabled",label:Be.SETTINGS.DISABLE_CLEANING,description:Be.SETTINGS.OUTPUT_BUFFER_CLEANING_DESCRIPTION,value:"1",checked:J,onChange:ee}))),Le=React.createElement(d.d,{title:Be.SETTINGS.PHP_ERROR_LOGS},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"php_error_logs",label:Be.SETTINGS.ENABLE,description:Be.SETTINGS.PHP_ERROR_LOGS_DESCRIPTION,value:"1",checked:$,onChange:ee}))),ke=React.createElement(d.d,{title:Be.SETTINGS.LOGS},React.createElement(E.E,{max:"1"},React.createElement(y.R,{name:"debuglogs",label:Be.SETTINGS.ENABLE,description:React.createElement(h.s,{p:!0},Be.SETTINGS.LOGS_DESCRIPTION),value:"1",checked:P,onChange:ee}),React.createElement("div",{style:{marginTop:20}},React.createElement(b.M,{onClick:ae},Be.SETTINGS.REFRESH_LOGS),React.createElement(b.M,{className:"danger",onClick:oe},Be.SETTINGS.CLEAR_LOGS)))),Pe=React.createElement(b.M,{className:"danger",disabled:p,onClick:re},Be.SETTINGS.RESET_OPTIONS),Me=React.createElement(b.M,{className:"blue",disabled:p,onClick:ce},Be.SETTINGS.EXPORT_OPTIONS),Fe=React.createElement(b.M,{className:"blue",disabled:p,onClick:le},Be.SETTINGS.IMPORT_OPTIONS);return React.createElement(R.z,null,React.createElement(ve.n,{isOpen:"resetDb"===f,onRequestClose:function(){m(null)},title:Be.SETTINGS.DONE,content:Be.SETTINGS.RESET_DB_CONTENT,onOkClick:function(){m(null)}}),React.createElement(g.n,{title:"".concat(Be.COMMON.PLUGIN_NAME," | ").concat(Be.COMMON.SETTINGS),subtitle:Be.COMMON.BY_JORDY_MEOW},React.createElement("div",{style:{display:"flex",justifyContent:"flex-end"}},React.createElement(b.M,{className:"header",onClick:function(){return window.open("https://meowapps.com/media-cleaner/tutorial/","_blank")}},Be.COMMON.TUTORIAL),React.createElement(b.M,{className:"header",icon:"dashboard",onClick:function(){return location.href="upload.php?page=wpmc_dashboard"}},Be.COMMON.DASHBOARD))),React.createElement(v.N,null,React.createElement(v.Y,{fullWidth:!0},React.createElement(Se.L,{footer:React.createElement(React.Fragment,null,React.createElement(b.M,{className:"primary",onClick:function(){return location.href="upload.php?page=wpmc_dashboard"}},Be.SETTINGS.ACCESS_DASHBOARD),React.createElement(b.M,{className:"danger",onClick:ne},Be.SETTINGS.DELETE_CLEANER_DB))},React.createElement(h.s,{p:!0},(0,u.FE)(Be.SETTINGS.SCAN_DESCRIPTION))),React.createElement(S._,{keepTabOnReload:!0},React.createElement(S.V,{title:Be.SETTINGS.MEDIA_LIBRARY_SCAN},React.createElement(v.N,null,React.createElement(v.Y,{minimal:!0},React.createElement(T.z,{busy:p,title:Be.SETTINGS.SCANNING,className:"primary"},se,(I||C)&&React.createElement(h.s,{p:!0},Be.SETTINGS.EITHER_SCANNING_DESCRIPTION),!I&&!C&&React.createElement(h.s,{p:!0},(0,u.FE)(Be.SETTINGS.NEITHER_SCANNING_DESCRIPTION)))),React.createElement(v.Y,{minimal:!0},React.createElement(T.z,{busy:p,title:Be.SETTINGS.FILTERS,className:"primary"},me),React.createElement(T.z,{busy:p,title:Be.SETTINGS.EXTRA_RULES,className:"primary"},pe)))),React.createElement(S.V,{title:Be.SETTINGS.FILESYSTEM_SCAN,requirePro:!x},React.createElement(v.N,null,React.createElement(v.Y,{minimal:!0},React.createElement(T.z,{busy:p,title:Be.SETTINGS.SCANNING,className:"primary"},fe,ue,React.createElement(h.s,{p:!0},Be.SETTINGS.FILESYSTEM_SCANNING_DESCRIPTION))),React.createElement(v.Y,{minimal:!0},React.createElement(T.z,{busy:p,title:Be.SETTINGS.FILTERS,className:"primary"},he,de,Ee)))),React.createElement(S.V,{title:Be.SETTINGS.ADVANCED},React.createElement(v.N,null,React.createElement(v.Y,{minimal:!0},React.createElement(T.z,{busy:p,title:Be.SETTINGS.UI_LOGS,className:"primary"},ye,Re,be,Te),React.createElement(T.z,{busy:p,title:Be.SETTINGS.ON_UNINSTALL,className:"primary"},_e),React.createElement(T.z,{busy:p,title:Be.SETTINGS.FOR_ADVANCED_USERS_DEVELOPERS,className:"primary"},Me,Fe,Pe)),React.createElement(v.Y,{minimal:!0},React.createElement(T.z,{busy:p,title:Be.SETTINGS.ADVANCED_SETTINGS,className:"primary"},we,Ie,Ae,Oe,Ne,xe,De)))),React.createElement(S.V,{title:Be.SETTINGS.LICENSE},React.createElement(Ce,{domain:w,prefix:_,isPro:N,isRegistered:x})),React.createElement(S.V,{title:Be.SETTINGS.LOGS_DEBUG},React.createElement(T.z,{busy:p,className:"primary"},P&&Le,ke),React.createElement("div",{style:Je},c))))))};var Qe,Ze,et,tt=r(6897),rt=r(1997),nt=r(8482),at=r(7494),ot=r(3502),it=r(851),ct=r(699),lt=r(1843),st=r(520),ut=r(1010),ft=r(365),mt=r(4876),pt=r(1479);function ht(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}var dt=M.Ay.img(Qe||(Qe=ht(["\n  width: 50px;\n  height: 50px;\n  border-radius: 4px;\n  object-fit: cover;\n  cursor: pointer;\n"]))),Et=M.Ay.div(Ze||(Ze=ht(["\n  width: 50px;\n  height: 50px;\n  border-radius: 4px;\n\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n  background-color: #f5f5f5;\n  color: #999;\n  font-size: 12px;\n  font-weight: 600;\n"]))),yt=M.Ay.span(et||(et=ht(["\n  color: ",";\n\n  border-radius: 4px;\n  padding: 2px 4px;\n\n  background-color: ",";\n"])),(function(e){return e.kb<1e3?"#00b100":e.kb<3e3?"#db8800":"#d90000"}),(function(e){return e.kb<1e3?"rgba(0, 255, 0, 0.11)":e.kb<3e3?"rgba(227, 186, 15, 0.18)":"rgba(255, 0, 0, 0.13)"})),Rt=r(1594),gt=r(5592);function vt(e){return vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vt(e)}function St(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function bt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?St(Object(r),!0).forEach((function(t){Tt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):St(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Tt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=vt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=vt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==vt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_t=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof y?t:y,i=Object.create(o.prototype),c=new x(n||[]);return a(i,"_invoke",{value:I(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var m="suspendedStart",p="suspendedYield",h="executing",d="completed",E={};function y(){}function R(){}function g(){}var v={};s(v,i,(function(){return this}));var S=Object.getPrototypeOf,b=S&&S(S(D([])));b&&b!==r&&n.call(b,i)&&(v=b);var T=g.prototype=y.prototype=Object.create(v);function _(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(a,o,i,c){var l=f(e[a],e,o);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==vt(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(u).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function I(t,r,n){var a=m;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===d){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=A(c,n);if(l){if(l===E)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var s=f(t,r,n);if("normal"===s.type){if(a=n.done?d:p,s.arg===E)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=d,n.method="throw",n.arg=s.arg)}}}function A(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),E;var o=f(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,E;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,E):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,E)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(vt(t)+" is not iterable")}return R.prototype=g,a(T,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:R,configurable:!0}),R.displayName=s(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===R||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,s(e,l,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},_(w.prototype),s(w.prototype,c,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new w(u(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},_(T),s(T,l,"Generator"),s(T,i,(function(){return this})),s(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,E):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),E},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),E}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),E}},t}function wt(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function It(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){wt(o,n,a,i,c,"next",e)}function c(e){wt(o,n,a,i,c,"throw",e)}i(void 0)}))}}function At(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,s=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Ot(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ot(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ot(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var Nt,xt,Dt=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=At((0,Rt.useState)(t),2),a=n[0],o=n[1],i=At((0,Rt.useState)(null),2),c=i[0],l=i[1],s=At((0,Rt.useState)(r),2),u=s[0],f=s[1],m=At((0,Rt.useState)(0),2),p=m[0],h=m[1];return(0,Rt.useEffect)((function(){void 0!==e&&(e.success?(l(null),o(e.data),h(e.total?e.total:0)):l(e.error)),f(void 0===e)}),[e]),{busy:u,data:a,total:p,error:c}},Lt={issues:"-",ignored:"-",trash:"-",references:"-"},kt={accessor:"size",by:"desc"},Ct=0,Pt={filterBy:"issues",sort:kt,page:1,limit:parseInt(L.posts_per_page),search:"",busy:!1,stats:Lt,apiErrors:null,status:"",options:L,referenceFilter:"showAll",entries:[],selectedItems:[],total:0,method:L.method,content:k,mediaLibrary:C,hideThumbnails:L.hide_thumbnails,hideWarning:L.hide_warning,skipTrash:L.skip_trash,postsBuffer:parseInt(L.posts_buffer),mediasBuffer:parseInt(L.medias_buffer),analysisBuffer:parseInt(L.analysis_buffer),fileOpBuffer:parseInt(L.file_op_buffer),delay:parseInt(L.delay),repairMode:L.repair_mode,expertMode:L.expert_mode,modals:{deleteAll:!1,trashAll:!1,forceTrashAll:!1,recoverAll:!1,showOriginalMedia:!1,pickFolder:!1,pickFolders:!1,extractReferencesForPostId:!1},mutateEntries:(xt=It(_t().mark((function e(){return _t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)}))),function(){return xt.apply(this,arguments)}),mutateStats:(Nt=It(_t().mark((function e(){return _t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)}))),function(){return Nt.apply(this,arguments)})},Mt="PAGE_UPDATED",Ft="LIMIT_UPDATED",Gt="FILTER_UPDATED",Bt="REFERENCE_FILTER_UPDATED",Ut="SORT_UPDATED",Ht="SEARCH_UPDATED",jt="TOTAL_UPDATED",Yt="STATUS_UPDATED",Vt="SETTINGS_UPDATED",Wt="TOGGLE_MODAL",zt="PUSH_BUSY",qt="POP_BUSY",Kt="ENTRIES_UPDATED",Xt="ITEMS_SELECTED",Jt="STATS_UPDATED",$t="ERROR_UPDATED",Qt="SET_ENTRIES_MUTATE",Zt="SET_STATS_MUTATE",er=function(e,t){switch(t.type){case Kt:var r=t.entries;return bt(bt({},e),{},{entries:r});case Jt:var n=t.stats;return bt(bt({},e),{},{stats:n});case $t:var a=t.apiErrors;return bt(bt({},e),{},{apiErrors:a});case Mt:var o=t.page;return bt(bt({},e),{},{page:o});case Qt:var i=t.mutateEntries;return bt(bt({},e),{},{mutateEntries:i});case Zt:var c=t.mutateStats;return bt(bt({},e),{},{mutateStats:c});case Ft:var l=t.limit;return bt(bt({},e),{},{limit:l});case Gt:var s=t.filter;return bt(bt({},e),{},{filterBy:s});case Ut:var f=t.sort,m=void 0===f?kt:f;return bt(bt({},e),{},{sort:m});case Ht:var p=t.search;return bt(bt({},e),{},{search:p});case jt:var h=t.total;return bt(bt({},e),{},{total:h});case Wt:var d=t.modal,E=t.enable;if(void 0===e.modals[d])return alert((0,u.g7)(Be.COMMON.MODAL_DOES_NOT_EXIST,d)),console.error((0,u.g7)(Be.COMMON.MODAL_DOES_NOT_EXIST,d)),e;void 0===E&&(E=!e.modals[d]);var y=bt({},e);return y.modals=bt({},y.modals),y.modals[d]=E,y;case zt:var R=t.status,g=void 0===R?"":R;return bt(bt({},e),{},{busy:++Ct>0,status:g});case qt:var v=t.status,S=void 0===v?"":v;return bt(bt({},e),{},{busy:--Ct>0,status:S});case Yt:var b=t.status,T=void 0===b?"":b;return bt(bt({},e),{},{status:T});case Vt:var _=t.options,w=_.method,I="files"===_.method?_.filesystem_content:_.content,A="files"===_.method&&(null==_?void 0:_.media_library),O=_.hide_thumbnails,N=_.hide_warning,x=_.skip_trash,D=_.posts_buffer?parseInt(_.posts_buffer):5,L=_.medias_buffer?parseInt(_.medias_buffer):100,k=_.analysis_buffer?parseInt(_.analysis_buffer):100,C=_.file_op_buffer?parseInt(_.file_op_buffer):20,P=_.delay?parseInt(_.delay):0,M=_.posts_per_page?parseInt(_.posts_per_page):10,F=_.repair_mode,G=_.expert_mode;return bt(bt({},e),{},{options:_,method:w,content:I,mediaLibrary:A,hideThumbnails:O,hideWarning:N,skipTrash:x,postsBuffer:D,mediasBuffer:L,analysisBuffer:k,fileOpBuffer:C,delay:P,limit:M,repairMode:F,expertMode:G});case Xt:var B=t.selectedItems;return bt(bt({},e),{},{selectedItems:B});case Bt:var U=t.referenceFilter;return bt(bt({},e),{},{referenceFilter:U});default:return e}},tr=(0,Rt.createContext)(),rr=function(e){var t=e.children,r=At((0,Rt.useReducer)(er,Pt),2),n=r[0],a=r[1],o=n.filterBy,i=n.sort,c=n.search,l=n.page,s=n.limit,f=n.referenceFilter,m=n.repairMode,p=(0,Rt.useMemo)((function(){return[(0,u.mR)("".concat(A,"/entries"),{limit:s,skip:(l-1)*s,filterBy:o,orderBy:i.accessor,order:i.by,search:c,referenceFilter:f,repairMode:m}),{headers:{"X-WP-Nonce":D}}]}),[o,i,c,l,s,A,D,u.mR,f,m]),h=(0,Rt.useMemo)((function(){return[(0,u.mR)("".concat(A,"/stats"),{search:c,referenceFilter:f,repairMode:m}),{headers:{"X-WP-Nonce":D}}]}),[c,f,A,D,u.mR,m]),d=(0,gt.Ay)(p,u.m9),E=d.data,y=d.mutate,R=Dt(E,[],!0),g=R.busy,v=R.data,S=R.total,b=R.error;(0,Rt.useEffect)((function(){a({type:Qt,mutateEntries:y})}),[y]),(0,Rt.useEffect)((function(){a({type:g?zt:qt})}),[g]),(0,Rt.useEffect)((function(){a({type:Kt,entries:v})}),[v]),(0,Rt.useEffect)((function(){a({type:jt,total:S})}),[S]),(0,Rt.useEffect)((function(){a({type:$t,apiError:b})}),[b]);var T=(0,gt.Ay)(h,u.m9),_=T.data,w=T.mutate,I=Dt(_,Lt,!0),O=I.busy,N=I.data,x=I.error;return(0,Rt.useEffect)((function(){a({type:Zt,mutateStats:w})}),[w]),(0,Rt.useEffect)((function(){a({type:O?zt:qt})}),[O]),(0,Rt.useEffect)((function(){a({type:Jt,stats:N})}),[N]),(0,Rt.useEffect)((function(){a({type:$t,apiError:x})}),[x]),React.createElement(tr.Provider,{value:[n,a]},t)};const nr=function(){var e={},t=At((0,Rt.useContext)(tr),2),r=t[0],n=t[1];return e.setSelectedItems=function(e){n({type:Xt,selectedItems:e})},e.setStatus=function(e){n({type:Yt,status:e})},e.updateOption=function(){var e=It(_t().mark((function e(t,a){var o,i,c,l=arguments;return _t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=l.length>2&&void 0!==l[2]&&l[2],n({type:zt}),i=bt(bt({},r.options),{},Tt({},a,t)),o&&(i=Tt({},a,t)),e.prev=4,e.next=7,(0,u.Tb)("".concat(A,"/update_options"),{json:{options:i},nonce:D});case 7:(c=e.sent).success&&n({type:Vt,options:c.options}),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),e.t0.message&&alert(e.t0.message);case 14:return e.prev=14,n({type:qt}),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[4,11,14,17]])})));return function(t,r){return e.apply(this,arguments)}}(),e.setPage=function(e){n({type:Mt,page:e})},e.setLimit=function(e){n({type:Ft,limit:e})},e.setFilter=function(e){n({type:Gt,filter:e})},e.setSort=function(e){n({type:Ut,sort:e})},e.setSearch=function(e){n({type:Ht,search:e})},e.setReferenceFilter=function(e){n({type:Bt,referenceFilter:e})},e.setRepairMode=function(t){e.updateOption(t,"repair_mode")},e.toggleModal=function(e){n({type:Wt,modal:e,enable:arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0})},e.pushBusy=function(){n({type:zt})},e.popBusy=function(){n({type:qt})},bt(bt({},r),e)};var ar=r(6691),or={fontSize:15},ir=function(){var e=nr(),t=e.hideWarning,r=e.method,n=e.content,a=e.mediaLibrary,o=L.incompatible_plugins,i=React.createElement(React.Fragment,null,React.createElement(b.M,{className:"secondary",onClick:function(){return window.open("https://meowapps.com/plugin/media-cleaner/","_blank")}},Be.NOTIFICATIONS.GET_PRO_VERSION),React.createElement(b.M,{className:"primary",onClick:function(){return window.open("https://meowapps.com/media-cleaner/tutorial/","_blank")}},Be.NOTIFICATIONS.READ_TUTORIAL));return React.createElement(React.Fragment,null,"media"===r&&!n&&React.createElement(ar.n,{variant:"info"},React.createElement(h.s,{p:!0,style:or},Be.NOTIFICATIONS.SCAN_MEDIA_LIBRARY_FOR_BROKEN_ENTRIES)),"media"===r&&n&&React.createElement(ar.n,{variant:"info"},React.createElement(h.s,{p:!0,style:or},Be.NOTIFICATIONS.SCAN_MEDIA_LIBRARY_FOR_UNUSED_ENTRIES)),"files"===r&&n&&a&&React.createElement(ar.n,{variant:"info"},React.createElement(h.s,{p:!0,style:or},Be.NOTIFICATIONS.SCAN_PHYSICAL_DIRS_FOR_UNREGISTERED_OR_UNUSED)),"files"===r&&!n&&a&&React.createElement(ar.n,{variant:"info"},React.createElement(h.s,{p:!0,style:or},Be.NOTIFICATIONS.SCAN_PHYSICAL_DIRS_FOR_UNREGISTERED)),"files"===r&&!n&&!a&&React.createElement(ar.n,{variant:"info"},React.createElement(h.s,{p:!0,style:or},Be.NOTIFICATIONS.RETURN_ALL_FILES_FROM_PHYSICAL_DIRS)),!t&&React.createElement(ar.n,{variant:"warning"},React.createElement(h.s,{p:!0},React.createElement("p",null,(0,u.FE)(Be.NOTIFICATIONS.WARNING_IMPORTANCE)),React.createElement("p",null,(0,u.FE)(Be.NOTIFICATIONS.WARNING_CONSIDERATION)))),!x&&React.createElement(Se.L,{footer:i},React.createElement(h.s,{p:!0},(0,u.FE)(Be.NOTIFICATIONS.FREE_VERSION_INSTRUNTION))),o&&o.length>0&&React.createElement(ar.n,{variant:"error"},React.createElement(h.s,{p:!0,style:or},React.createElement("p",null,Be.NOTIFICATIONS.IMCOMPATIBLE_PLUGINS_NOTE),React.createElement("ul",null,o.map((function(e){return React.createElement("li",null,"- ",e)}))),React.createElement("p",null,(0,u.FE)(Be.NOTIFICATIONS.IMCOMPATIBLE_PLUGINS_MESSAGE)))))},cr=function(e){return new Promise((function(t){return setTimeout(t,e)}))},lr=function(e){for(var t=[],r=function(){var r=e[n].split("/").filter((function(e){return!!e})),a=r[0];if(t.some((function(e){return e.name===a}))||t.push({name:a,children:[]}),1===r.length)return 1;for(var o=t.find((function(e){return e.name===a})),i=function(){var e=r[c];o.children.some((function(t){return t.name===e}))||o.children.push({name:e,children:[]}),o=o.children.find((function(t){return t.name===e}))},c=1;c<r.length;c++)i()},n=0;n<e.length;n++)r();return t};function sr(e){return sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sr(e)}function ur(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=sr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sr(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fr(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=yr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){c=!0,o=e},f:function(){try{i||null==r.return||r.return()}finally{if(c)throw o}}}}function mr(e){return function(e){if(Array.isArray(e))return Rr(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||yr(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pr(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */pr=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof y?t:y,i=Object.create(o.prototype),c=new x(n||[]);return a(i,"_invoke",{value:I(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var m="suspendedStart",p="suspendedYield",h="executing",d="completed",E={};function y(){}function R(){}function g(){}var v={};s(v,i,(function(){return this}));var S=Object.getPrototypeOf,b=S&&S(S(D([])));b&&b!==r&&n.call(b,i)&&(v=b);var T=g.prototype=y.prototype=Object.create(v);function _(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(a,o,i,c){var l=f(e[a],e,o);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==sr(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(u).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function I(t,r,n){var a=m;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===d){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=A(c,n);if(l){if(l===E)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var s=f(t,r,n);if("normal"===s.type){if(a=n.done?d:p,s.arg===E)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=d,n.method="throw",n.arg=s.arg)}}}function A(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),E;var o=f(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,E;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,E):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,E)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(sr(t)+" is not iterable")}return R.prototype=g,a(T,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:R,configurable:!0}),R.displayName=s(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===R||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,s(e,l,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},_(w.prototype),s(w.prototype,c,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new w(u(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},_(T),s(T,l,"Generator"),s(T,i,(function(){return this})),s(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,E):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),E},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),E}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),E}},t}function hr(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function dr(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){hr(o,n,a,i,c,"next",e)}function c(e){hr(o,n,a,i,c,"throw",e)}i(void 0)}))}}function Er(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,s=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return c}}(e,t)||yr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yr(e,t){if(e){if("string"==typeof e)return Rr(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Rr(e,t):void 0}}function Rr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var gr=wp.element,vr=gr.useState,Sr=gr.useEffect,br=gr.useMemo,Tr=gr.useCallback,_r=[{id:"media",value:"media",label:Be.COMMON.MEDIA_LIBRARY,requirePro:!1},{id:"files",value:"files",label:Be.COMMON.FILESYSTEM,requirePro:!x}],wr=[{value:"showAll",label:Be.DASHBOARD.REFERENCE_COLUMN_FILTERS.SHOW_ALL},{value:"mediaIds",label:Be.DASHBOARD.REFERENCE_COLUMN_FILTERS.ONLY_MEDIA_IDS},{value:"mediaUrls",label:Be.DASHBOARD.REFERENCE_COLUMN_FILTERS.ONLY_MEDIA_URLS}],Ir={NO_CONTENT:Be.ISSUE_CODES.NO_CONTENT,ORPHAN_MEDIA:Be.ISSUE_CODES.ORPHAN_MEDIA,ORPHAN_FILE:Be.ISSUE_CODES.ORPHAN_FILE,ORPHAN_RETINA:Be.ISSUE_CODES.ORPHAN_RETINA,ORPHAN_WEBP:Be.ISSUE_CODES.ORPHAN_WEBP};const Ar=function(){var e=nr(),t=e.apiErrors,r=e.busy,n=e.selectedItems,a=e.entries,o=e.stats,i=e.total,c=e.filterBy,l=e.search,s=e.status,f=e.sort,m=e.page,p=e.limit,d=e.method,E=e.content,y=e.mediaLibrary,S=e.hideThumbnails,T=e.postsBuffer,_=e.mediasBuffer,w=e.analysisBuffer,I=e.fileOpBuffer,O=e.delay,N=e.modals,L=e.referenceFilter,k=e.repairMode,C=e.expertMode,P=nr(),M=P.setSelectedItems,F=P.setSearch,G=P.setStatus,B=P.setSort,U=P.setPage,H=P.setLimit,j=P.setFilter,Y=P.setReferenceFilter,V=P.mutateEntries,W=P.mutateStats,z=P.pushBusy,q=P.popBusy,K=P.toggleModal,X=P.updateOption,J=P.setRepairMode,$=Er(vr(1),2),Q=$[0],Z=$[1],ee=Er(vr(1),2),te=ee[0],re=ee[1],ne=Er(vr(""),2),ae=ne[0],oe=ne[1],ie=Er(vr(!1),2),ce=ie[0],le=ie[1],se=Er(vr(!1),2),ue=(se[0],se[1]),fe=(0,tt.XS)({onStop:function(){he(),ue()}}),me=Er(vr(),2),pe=me[0],he=me[1],de=Er(vr(!1),2),Ee=de[0],ye=de[1],Re=Er(vr(k),2),Se=Re[0],be=Re[1],Te=Er(vr(null),2),_e=Te[0],we=Te[1],Ie=Er(vr({root:null,hierarchy:[]}),2),Ae=Ie[0],Oe=Ie[1],Ne=Er(vr(""),2),xe=Ne[0],De=Ne[1],Le=Er(vr(null),2),ke=Le[0],Ce=Le[1],Pe=Er(vr(null),2),Me=Pe[0],Fe=Pe[1],Ge=Tr(dr(pr().mark((function e(){var t,r,n,a,o,i,c=arguments;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]&&c[0],e.prev=1,r=(0,u.mR)("".concat(A,"/uploads_directory_hierarchy"),{force:t}),e.next=5,(0,u.IU)(r,{nonce:D});case 5:return n=e.sent,a=JSON.parse(n.data.hierarchy),o=lr(a),i={root:n.data.root,hierarchy:o},Oe(i),De(n.data.root),e.abrupt("return",i);case 14:e.prev=14,e.t0=e.catch(1),alert(e.t0.message);case 17:case"end":return e.stop()}}),e,null,[[1,14]])}))),[]),Ue=br((function(){var e="trash"===c,t="references"===c;return a.map((function(r){var n,a,o=null!==(n=r.originType)&&void 0!==n?n:"",i=null!==(a=null==r?void 0:r.post_title)&&void 0!==a?a:"...",c=o.match(/\[(\d+)\]/);if(c){var l=c[1],s="/wp-admin/post.php?post=".concat(l,"&action=edit");o=(o=o.replace(c[0],"")).trim(),i=React.createElement(React.Fragment,null,React.createElement("span",null,i)," ",React.createElement("small",null,React.createElement("a",{href:s,target:"_blank",rel:"noreferrer"},"(ID #",l,")")))}o.includes("{SAFE}")?(o=(o=o.replace("{SAFE}","")).trim(),o=React.createElement("span",null,o," ",React.createElement(rt.f,{text:"Even if this was not directly detected as in use, just to be sure it will not be deleted."},React.createElement("small",{style:{color:"rgb(59, 142, 79)",backgroundColor:"#d7ffdc",borderRadius:"5px",padding:"2px 4px",cursor:"help"}},"SAFE")))):o=React.createElement("span",null,o);var u=null!=r&&r.thumbnail?React.createElement(dt,{src:r.thumbnail,onClick:function(){return we(r.thumbnail)}}):React.createElement(Et,null,"⚠️");if(t)return{thumbnail:u,post:i,mediaId:r.mediaId,mediaUrl:r.mediaUrl,originType:o};var f="_blank",m="/wp-admin/",p=window.location.href.split("/")[3];"wp-admin"!==p&&(m="/"+p+m);var h=e?r.image_url:"".concat(m,"post.php?post=").concat(r.postId,"&action=edit"),d=r.child_paths?r.child_paths.split(",").join("\n"):null,E=r.thumbnail_url?React.createElement(dt,{src:r.thumbnail_url,onClick:function(){return we(r.image_url)}}):React.createElement(Et,null,"⚠️");return{id:r.id,thumbnail_url:React.createElement(React.Fragment,null,"ORPHAN_MEDIA"!==r.issue&&E),type:React.createElement("span",null,0==r.type&&"Filesystem",1==r.type&&React.createElement("a",{href:h,target:f},"ID ",r.postId)),path:React.createElement("div",{style:{display:"flex",flexDirection:"column"}},r.title&&React.createElement("a",{href:h,target:f},React.createElement("span",null,r.title)),React.createElement("span",null,r.path),k&&d&&React.createElement("span",{style:{fontSize:12,color:"#999",whiteSpace:"pre"}},d)),issue:React.createElement("span",null,Ir[r.issue]?Ir[r.issue]:r.issue),size:React.createElement(yt,{kb:(r.size/1e3).toFixed(2)},(r.size/1e3).toFixed(2)," ",React.createElement("b",null,"KB")," "),action:React.createElement(b.M,{onClick:function(){return Je(r.id)}},Be.DASHBOARD.REPAIR)}}))}),[a,c,L,k]),He=br((function(){if("references"===c)return[{accessor:"thumbnail",title:Be.DASHBOARD.THUMBNAIL,sortable:!1},{accessor:"post",title:Be.DASHBOARD.POST,sortable:!0},{accessor:"originType",title:Be.DASHBOARD.ORIGIN_TYPE,sortable:!0},{accessor:"mediaId",title:Be.DASHBOARD.MEDIA_ID,sortable:!0},{accessor:"mediaUrl",title:Be.DASHBOARD.MEDIA_URL,sortable:!0}];var e=[{accessor:"type",title:Be.DASHBOARD.ORIGIN,sortable:!0},{accessor:"path",title:Be.DASHBOARD.TITLE_PATH,sortable:!0},{accessor:"issue",title:Be.DASHBOARD.ISSUE,sortable:!0},{accessor:"size",title:Be.DASHBOARD.SIZE,sortable:!0}];return S||e.unshift({accessor:"thumbnail_url",title:"",style:{width:16}}),k&&e.push({accessor:"action",title:Be.DASHBOARD.ACTION}),e}),[S,c,k]);Sr((function(){x&&Ge()}),[]),Sr((function(){_e&&K("showOriginalMedia",!0)}),[_e]),Sr((function(){M([]),B(),U(1)}),[c]);var je=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;(e=null!==e?e:fe.getErrorCount())>0&&oe((0,u.g7)(Be.DASHBOARD.FINAL_MESSAGE,e)),fe.reset(),M([]),V(),W()},Ye=function(){var e=dr(pr().mark((function e(t){var r,n,a,o=arguments;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=o.length>1&&void 0!==o[1]?o[1]:null,n=o.length>2&&void 0!==o[2]&&o[2],e.next=4,(0,u.IU)("".concat(A,"/all_ids"),{json:{source:t,search:r,repairMode:n},nonce:D,method:"POST"});case 4:if((a=e.sent).success){e.next=8;break}return alert(a.message),e.abrupt("return",[]);case 8:return e.abrupt("return",a.data);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ve=function(){var e=dr(pr().mark((function e(t){var r;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(O>0)){e.next=3;break}return e.next=3,cr(O);case 3:return e.next=5,(0,u.IU)("".concat(A,"/count"),{json:{source:t},nonce:D,method:"POST"});case 5:return r=e.sent,e.abrupt("return",r.data);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),We=function(){var e=dr(pr().mark((function e(){var t,r,n,a,o,i,c,l=arguments;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=l.length>0&&void 0!==l[0]?l[0]:null,r=l.length>1&&void 0!==l[1]&&l[1],n=l.length>2&&void 0!==l[2]?l[2]:null,G(Be.DASHBOARD.STATUS_GETTING_IDS),z(),!t){e.next=9;break}e.t0=t,e.next=12;break;case 9:return e.next=11,Ye(r?"trash":"issues",n);case 11:e.t0=e.sent;case 12:if(!(a=e.t0)){e.next=20;break}return G(r?Be.DASHBOARD.STATUS_EMPTYING:Be.DASHBOARD.STATUS_DELETING),o=(0,u.a4)(a,I),i=o.map((function(e){return function(){var t=dr(pr().mark((function t(r){return pr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(O>0)){t.next=3;break}return t.next=3,cr(O);case 3:return t.next=5,(0,u.IU)("".concat(A,"/delete"),{json:{entryIds:e},signal:r,nonce:D,method:"POST"});case 5:return t.abrupt("return",t.sent);case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),e.next=19,fe.start(i);case 19:je();case 20:if(null!=t||!r){e.next=31;break}return e.prev=21,e.next=24,(0,u.IU)("".concat(A,"/stats"),{nonce:D});case 24:(c=e.sent).success&&c.data&&c.data.trash>0&&K("forceTrashAll",!0),e.next=31;break;case 28:e.prev=28,e.t1=e.catch(21),console.error("Failed to get latest stats:",e.t1);case 31:q(),G();case 33:case"end":return e.stop()}}),e,null,[[21,28]])})));return function(){return e.apply(this,arguments)}}(),ze=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return G(Be.DASHBOARD.STATUS_EMPTYING),z(),e.prev=2,e.next=5,(0,u.IU)("".concat(A,"/force_trash_all"),{nonce:D,method:"POST"});case 5:e.sent.success&&(alert(Be.DASHBOARD.STATUS_EMPTYING_SUCCESS),window.location.reload()),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),alert(e.t0.message);case 12:q(),G();case 14:case"end":return e.stop()}}),e,null,[[2,9]])})));return function(){return e.apply(this,arguments)}}(),qe=function(){var e=dr(pr().mark((function e(){var t,r,n,a,o=arguments;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=o.length>0&&void 0!==o[0]?o[0]:null,G(Be.DASHBOARD.STATUS_GETTING_IDS),z(),!t){e.next=7;break}e.t0=t,e.next=10;break;case 7:return e.next=9,Ye("trash");case 9:e.t0=e.sent;case 10:if(!(r=e.t0)){e.next=17;break}return n=(0,u.a4)(r,I),a=n.map((function(e){return function(){var t=dr(pr().mark((function t(r){return pr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(G(Be.DASHBOARD.STATUS_RECOVERING),!(O>0)){t.next=4;break}return t.next=4,cr(O);case 4:return t.next=6,(0,u.IU)("".concat(A,"/recover"),{json:{entryIds:e},signal:r,nonce:D,method:"POST"});case 6:return t.abrupt("return",t.sent);case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),e.next=16,fe.start(a);case 16:je();case 17:q(),G();case 19:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ke=function(){var e=dr(pr().mark((function e(){var t,r,n,a,o,i=arguments;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=!(i.length>0&&void 0!==i[0])||i[0],r=i.length>1&&void 0!==i[1]?i[1]:null,G(Be.DASHBOARD.STATUS_GETTING_IDS),z(),!r){e.next=8;break}e.t0=r,e.next=11;break;case 8:return e.next=10,Ye("issues");case 10:e.t0=e.sent;case 11:if(!(n=e.t0)){e.next=19;break}return G("".concat(t?Be.DASHBOARD.STATUS_IGNORING:Be.DASHBOARD.STATUS_UNDOING)),a=(0,u.a4)(n,w),o=a.map((function(e){return function(){var r=dr(pr().mark((function r(n){return pr().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!(O>0)){r.next=3;break}return r.next=3,cr(O);case 3:return r.next=5,(0,u.IU)("".concat(A,"/set_ignore"),{json:{entryIds:e,ignore:t},signal:n,nonce:D,method:"POST"});case 5:return r.abrupt("return",r.sent);case 6:case"end":return r.stop()}}),r)})));return function(e){return r.apply(this,arguments)}}()})),e.next=18,fe.start(o);case 18:je();case 19:q(),G();case 21:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Xe=function(){var e=dr(pr().mark((function e(t){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:X(t,"posts_per_page");case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Je=function(){var e=dr(pr().mark((function e(){var t,r,n,a,o=arguments;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=o.length>0&&void 0!==o[0]?o[0]:null,G(Be.DASHBOARD.STATUS_GETTING_IDS),z(),!t){e.next=7;break}e.t0=t,e.next=10;break;case 7:return e.next=9,Ye("issues",null,!0);case 9:e.t0=e.sent;case 10:if(!(r=e.t0)){e.next=17;break}return n=(0,u.a4)(r,I),a=n.map((function(e){return function(){var t=dr(pr().mark((function t(r){return pr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(G(Be.DASHBOARD.STATUS_RECOVERING),!(O>0)){t.next=4;break}return t.next=4,cr(O);case 4:return t.next=6,(0,u.IU)("".concat(A,"/repair"),{json:{entryIds:e},signal:r,nonce:D,method:"POST"});case 6:return t.abrupt("return",t.sent);case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),e.next=16,fe.start(a);case 16:je();case 17:q(),G();case 19:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),$e=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:fe.stop();case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Qe=function(){$e(),je(),q(),G()},Ze=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return G(Be.DASHBOARD.STATUS_RESETTING_ISSUES_AND_REFERENCES),e.next=3,(0,u.IU)("".concat(A,"/reset_issues_and_references"),{nonce:D,method:"POST"});case 3:Z((function(e){return e+1}));case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),et=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return G(Be.DASHBOARD.STATUS_RESETTING_ISSUES),e.next=3,(0,u.IU)("".concat(A,"/reset_issues"),{nonce:D,method:"POST"});case 3:Z((function(e){return e+1}));case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ht=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return G(Be.DASHBOARD.STATUS_RESETTING_REFERENCES),e.next=3,(0,u.IU)("".concat(A,"/reset_references"),{nonce:D,method:"POST"});case 3:Z((function(e){return e+1}));case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Rt=function(){var e=dr(pr().mark((function e(t,r){var n,a,o,i;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return G(Be.DASHBOARD.STATUS_EXTRACT_FROM_CONTENT),e.next=3,Ve("posts");case 3:for(n=e.sent,a=[],o=0;o<n;o+=T)a.push(o);return i=a.map((function(e){return function(){var t=dr(pr().mark((function t(n){return pr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(O>0)){t.next=3;break}return t.next=3,cr(O);case 3:return t.next=5,(0,u.IU)("".concat(A,"/extract_references"),{json:{source:"content",limit:e,postId:r},signal:n,nonce:D,method:"POST"});case 5:return t.abrupt("return",t.sent);case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),e.next=9,fe.start(i);case 9:fe.getErrorCount(),fe.reset(),Z((function(e){return e+1}));case 12:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),gt=function(){var e=dr(pr().mark((function e(t,r){var n,a,o,i;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return G(Be.DASHBOARD.STATUS_EXTRACT_FROM_LIBRARY),e.next=3,Ve("medias");case 3:for(n=e.sent,a=[],o=0;o<n;o+=T)a.push(o);return i=a.map((function(e){return function(){var t=dr(pr().mark((function t(n){return pr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(O>0)){t.next=3;break}return t.next=3,cr(O);case 3:return t.next=5,(0,u.IU)("".concat(A,"/extract_references"),{json:{source:"media",limit:e,postId:r},signal:n,nonce:D,method:"POST"});case 5:return t.abrupt("return",t.sent);case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),e.next=9,fe.start(i);case 9:fe.reset(),fe.getErrorCount(),Z((function(e){return e+1}));case 12:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),vt=function(){var e=dr(pr().mark((function e(t){var r,n,a,o,i;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=[],G(Be.DASHBOARD.STATUS_LISTING_MEDIA),e.next=4,Ve("medias");case 4:for(n=e.sent,a=[],o=0;o<n;o+=_)a.push(o);return i=a.map((function(e){return function(){var t=dr(pr().mark((function t(n){var a;return pr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(O>0)){t.next=3;break}return t.next=3,cr(O);case 3:return t.next=5,(0,u.IU)("".concat(A,"/retrieve_medias"),{json:{limit:e},signal:n,nonce:D,method:"POST"});case 5:return(a=t.sent).data&&r.push.apply(r,mr(a.data.results)),t.abrupt("return",a);case 8:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),e.next=10,fe.start(i);case 10:return fe.reset(),fe.getErrorCount(),Z((function(e){return e+1})),e.abrupt("return",r);case 14:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),St=function(){var e=dr(pr().mark((function e(t){var r,n,a,o=arguments;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=o.length>1&&void 0!==o[1]?o[1]:null,n=[],G(Be.DASHBOARD.STATUS_LISTING_FILES),a=function(){var e=dr(pr().mark((function e(t,r){var o,i,c,l,s;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,u.IU)("".concat(A,"/retrieve_files"),{json:{path:t},signal:r,nonce:D,method:"POST"});case 2:o=e.sent,i=o.data,c=fr(i.results),e.prev=5,s=pr().mark((function e(){var t;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:"dir"===(t=l.value).type?fe.addTask(dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a(t.path,r);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))):n.push(t.path);case 2:case"end":return e.stop()}}),e)})),c.s();case 8:if((l=c.n()).done){e.next=12;break}return e.delegateYield(s(),"t0",10);case 10:e.next=8;break;case 12:e.next=17;break;case 14:e.prev=14,e.t1=e.catch(5),c.e(e.t1);case 17:return e.prev=17,c.f(),e.finish(17);case 20:return e.abrupt("return",o);case 21:case"end":return e.stop()}}),e,null,[[5,14,17,20]])})));return function(t,r){return e.apply(this,arguments)}}(),!ke){e.next=10;break}return console.log("🟢 Starting task with a non default root:",ke),e.next=8,fe.start([function(){var e=dr(pr().mark((function e(t){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a(ke,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()]);case 8:e.next=12;break;case 10:return e.next=12,fe.start([function(){var e=dr(pr().mark((function e(t){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a(r,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()]);case 12:return fe.reset(),t+=fe.getErrorCount(),Z((function(e){return e+1})),e.abrupt("return",n);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),bt=function(){var e=dr(pr().mark((function e(t,r){var n,a;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return G(Be.DASHBOARD.STATUS_MATCHING_WITH_CONTENT),n=(0,u.a4)(t,w),a=n.map((function(e){return function(){var t=dr(pr().mark((function t(r){return pr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(O>0)){t.next=3;break}return t.next=3,cr(O);case 3:return t.next=5,(0,u.IU)("".concat(A,"/check_targets"),{json:{method:d,targets:e},signal:r,nonce:D,method:"POST"});case 5:return t.abrupt("return",t.sent);case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),e.next=5,fe.start(a);case 5:fe.getErrorCount();case 6:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),Tt=function(){G(Be.DASHBOARD.STATUS_SETTING_REPAIR_MODE),J(!0)},_t=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return z(),he(Date.now()),ue(!0),M([]),Z(1),re(2),e.next=8,et();case 8:je(),he(),ue(),q(),G();case 13:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),wt=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return z(),he(Date.now()),ue(!0),M([]),Z(1),re(2),e.next=8,ht();case 8:je(),he(),ue(),q(),G();case 13:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),It=function(){var e=dr(pr().mark((function e(){var t,r,n=arguments;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:null,r=0,z(),he(Date.now()),ue(!0),M([]),Z(1),re(2+("files"===d&&y?1:0)),e.next=10,Ze();case 10:if(!E){e.next=13;break}return e.next=13,Rt(r,t);case 13:if("files"!==d||!y){e.next=16;break}return e.next=16,gt(r,t);case 16:je(r),he(),ue(),q(),G();case 21:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),At=function(){var e=dr(pr().mark((function e(){var t,r;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0,z(),he(Date.now()),ue(!0),M([]),Z(1),re(2),r=[],"media"!==d){e.next=14;break}return e.next=11,vt(t);case 11:r=e.sent,e.next=21;break;case 14:if("files"!==d){e.next=20;break}return e.next=17,St(t,xe.replace(Ae.root,""));case 17:r=e.sent,e.next=21;break;case 20:alert(Be.DASHBOARD.STATUS_ERROR_UNKNOWN_METHOD);case 21:if(!r){e.next=24;break}return e.next=24,bt(r,t);case 24:Se&&Tt(),je(t),he(),ue(),q(),G(),"files"===d&&De(Ae.root);case 31:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ot=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,X(d,"method",!0);case 2:return e.next=4,xt(Se);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Nt=function(){var e=dr(pr().mark((function e(){return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Ge(!0);case 2:e.sent,K("pickFolders",!0);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),xt=function(){var e=dr(pr().mark((function e(t){var r,n;return pr().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=0,z(),he(Date.now()),le(!0),M([]),Z(1),re(3+(E?1:0)+("files"===d&&y?1:0)),e.next=9,Ze();case 9:if(!E){e.next=12;break}return e.next=12,Rt(r);case 12:if("files"!==d||!y){e.next=15;break}return e.next=15,gt(r);case 15:if(n=[],"media"!==d){e.next=22;break}return e.next=19,vt(r);case 19:n=e.sent,e.next=29;break;case 22:if("files"!==d){e.next=28;break}return e.next=25,St(r);case 25:n=e.sent,e.next=29;break;case 28:alert(Be.DASHBOARD.STATUS_ERROR_UNKNOWN_METHOD);case 29:if(!n){e.next=32;break}return e.next=32,bt(n,r);case 32:t&&Tt(),je(r),he(),le(),q(),G();case 38:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Dt=React.createElement("div",{style:{display:"flex",alignItems:"center"}},"| ",React.createElement("span",{style:{fontWeight:"bold",margin:"0 2px 0 5px"}},"Base Folder:"),!ke&&React.createElement(React.Fragment,null,React.createElement("span",null," (default)"),React.createElement(b.M,{style:{marginLeft:3},className:"secondary",disabled:r||ce,onClick:function(){return Nt()}},Be.DASHBOARD.PICK)),ke&&React.createElement(React.Fragment,null,React.createElement("span",null," /",ke),React.createElement(b.M,{small:!0,style:{marginLeft:3},className:"secondary",disabled:r||ce,onClick:function(){return Ce(null)}},Be.DASHBOARD.RESET))),Lt="files"===d&&React.createElement(React.Fragment,null,React.createElement(nt.S,{small:!0,onLabel:"Repair Mode",offLabel:"Repair Mode",onValue:"1",offValue:"0",width:"100px",checked:Se,onChange:function(){J(!Se),be(!Se)}}),React.createElement(pt.G,{position:"right",content:Be.DASHBOARD.REPAIR_MODE_DESCRIPTION,tooltipMaxWidth:300,color:k?"blue":null}));return React.createElement(R.z,{nekoErrors:[t]},React.createElement(g.n,{title:"".concat(Be.COMMON.PLUGIN_NAME," | ").concat(Be.COMMON.DASHBOARD),subtitle:Be.COMMON.BY_JORDY_MEOW},React.createElement("div",{style:{display:"flex",justifyContent:"flex-end"}},React.createElement(b.M,{className:"header",onClick:function(){return window.open("https://meowapps.com/media-cleaner/tutorial/","_blank")}},Be.COMMON.TUTORIAL),React.createElement(b.M,{icon:"cog",className:"header",onClick:function(){return location.href="admin.php?page=wpmc_settings"}},Be.COMMON.SETTINGS))),React.createElement(v.N,null,React.createElement(v.Y,{fullWidth:!0},React.createElement(ir,null),React.createElement(at.V,null,!n.length&&"references"!==c&&React.createElement(React.Fragment,null,React.createElement(b.M,{icon:"play",disabled:r,isBusy:ce,startTime:pe,onClick:function(){return Ot()}},Be.DASHBOARD.SCAN),!s&&React.createElement(ot.u,{scrolldown:!0,disabled:r,name:"method",style:{width:200,marginLeft:".25rem"},onChange:function(e,t){"media"===e&&(be(!1),J(!1)),X(e,t,!0)},value:d},_r.map((function(e){return React.createElement(ot.j,{key:e.id,id:e.id,value:e.value,label:e.label,requirePro:e.requirePro})})))),!n.length&&"references"===c&&React.createElement(React.Fragment,null,React.createElement(b.M,{icon:"replay",disabled:!0,onClick:function(){return ye(!0)}},Be.DASHBOARD.RE_ATTACH_MEDIA),React.createElement(ot.u,{scrolldown:!0,value:L,onChange:function(e){return Y(e)},style:{width:200}},wr.map((function(e){return React.createElement(ot.j,{key:e.value,value:e.value,label:e.label})})))),n.length>0&&React.createElement(b.M,{disabled:r,onClick:function(){return M([])}},"Unselect All"),s&&React.createElement("div",{style:ur(ur(ur(ur(ur({display:"flex",fontFamily:"Lato",padding:"0 10px",flexDirection:"column",fontWeight:600,width:200},"flexDirection","column"),"height",30),"lineHeight","16px"),"justifyContent","center"),"textTransform","uppercase")},React.createElement("div",{style:{color:"#144675",fontSize:17}},"".concat(Be.DASHBOARD.STEP," ").concat(Q,"/").concat(te)),React.createElement("div",{style:{color:"#007bba"}},s)),!r&&"issues"===c&&n.length>0&&React.createElement(React.Fragment,null,React.createElement(b.M,{className:"secondary",onClick:function(){return Ke(!0,n)}},(0,u.FE)((0,u.g7)(Be.DASHBOARD.IGNORE_SELECTED,n.length))),React.createElement(b.M,{className:"danger",onClick:function(){return We(n)}},(0,u.FE)((0,u.g7)(Be.DASHBOARD.DELETE_SELECTED,n.length))),React.createElement(b.M,{className:"secondary",onClick:function(){return Je(n)}},(0,u.FE)((0,u.g7)(Be.DASHBOARD.REPAIR_SELECTED,n.length)))),!r&&"ignored"===c&&n.length>0&&React.createElement(React.Fragment,null,React.createElement(b.M,{className:"secondary",onClick:function(){return Ke(!1,n)}},(0,u.FE)((0,u.g7)(Be.DASHBOARD.UNDO_SELECTED,n.length)))),!r&&"trash"===c&&n.length>0&&React.createElement(React.Fragment,null,React.createElement(b.M,{className:"secondary",onClick:function(){return qe(n)}},(0,u.FE)((0,u.g7)(Be.DASHBOARD.RECOVER_SELECTED,n.length))),React.createElement(b.M,{icon:"delete",className:"danger",onClick:function(){return We(n)}},(0,u.FE)((0,u.g7)(Be.DASHBOARD.DELETE_PERMANENTLY_SELECTED,n.length)))),React.createElement("div",{style:{flex:1}},s&&React.createElement(it.j,{status:function(e){return"".concat(fe.max?"".concat(e,"%"):"+=")},busy:fe.busy,value:fe.value,max:fe.max,onStopClick:fe.stop})),!r&&React.createElement(React.Fragment,null,React.createElement(ge.A,{name:"search",onBlur:function(e){F(e)},onEnter:function(e){F(e)},value:l,onReset:function(){F("")}}),!l&&React.createElement(ct.z,{icon:"search",width:24,style:{position:"relative",left:-34,marginRight:-22},color:"#5a5a5a82"})),!r&&"issues"===c&&o.issues>0&&React.createElement(React.Fragment,null,React.createElement(b.M,{icon:"delete",className:"danger",onClick:function(){return K("deleteAll")}},(0,u.FE)(Be.DASHBOARD.DELETE_STRONG_ALL))),!r&&"trash"===c&&o.trash>0&&React.createElement(React.Fragment,null,React.createElement(b.M,{className:"primary",onClick:function(){return K("recoverAll")}},(0,u.FE)(Be.DASHBOARD.RECOVER_ALL)),React.createElement(b.M,{icon:"delete",className:"danger",onClick:function(){return K("trashAll")}},Be.DASHBOARD.EMPTY_TRASH))),React.createElement("p",null,(0,u.FE)((0,u.g7)(Be.DASHBOARD.INFO_MESSAGE,o.issues,o.issues>1?"s":"",(o.issues_size/1e6).toFixed(2),(o.trash_size/1e6).toFixed(2)))),React.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginTop:10,marginBottom:10}},k&&React.createElement(lt.o,{value:"issues"},React.createElement(lt.K,{title:Be.DASHBOARD.ISSUES,value:"issues",count:o.issues})),!k&&React.createElement(React.Fragment,null,React.createElement(lt.o,{name:"wpmc-filter",value:c,busy:r,onChange:function(e){j(e),U(1)}},React.createElement(lt.K,{title:Be.DASHBOARD.ISSUES,value:"issues",count:o.issues}),React.createElement(lt.K,{title:Be.DASHBOARD.IGNORED,value:"ignored",count:o.ignored}),React.createElement(lt.K,{title:Be.DASHBOARD.TRASH,value:"trash",count:o.trash}),React.createElement(lt.K,{title:Be.DASHBOARD.REFERENCES,value:"references",count:o.references}))),React.createElement(st.Q,{currentPage:m,limit:p,total:i,onClick:function(e){return U(e)}})),React.createElement(ut.o,{busy:r,data:Ue,columns:He,sort:f,onSortChange:function(e,t){B({accessor:e,by:t})},onSelect:function(e){M([].concat(mr(n),mr(e)))},onUnselect:function(e){M(mr(n.filter((function(t){return!e.includes(t)}))))},selectedItems:n,emptyMessage:React.createElement(React.Fragment,null,(0,u.FE)(Be.DASHBOARD.EMPTY_MESSAGE))}),React.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginTop:10}},React.createElement("div",{style:{display:"flex",alignItems:"center",gap:"4px"}},React.createElement(ot.u,{name:"limit",scrolldown:!0,value:p,onChange:function(e){H(e),U(1),Xe(e)}},React.createElement(ot.j,{value:5,label:"5 ".concat(Be.DASHBOARD.ITEMS_PAGE)}),React.createElement(ot.j,{value:10,label:"10 ".concat(Be.DASHBOARD.ITEMS_PAGE)}),React.createElement(ot.j,{value:25,label:"25 ".concat(Be.DASHBOARD.ITEMS_PAGE)}),React.createElement(ot.j,{value:50,label:"50 ".concat(Be.DASHBOARD.ITEMS_PAGE)}),React.createElement(ot.j,{value:100,label:"100 ".concat(Be.DASHBOARD.ITEMS_PAGE)}),React.createElement(ot.j,{value:250,label:"250 ".concat(Be.DASHBOARD.ITEMS_PAGE)}),React.createElement(ot.j,{value:500,label:"500 ".concat(Be.DASHBOARD.ITEMS_PAGE)}),React.createElement(ot.j,{value:1e3,label:"1000 ".concat(Be.DASHBOARD.ITEMS_PAGE)})),Lt,"files"===d&&Dt),React.createElement(st.Q,{currentPage:m,limit:p,total:i,onClick:function(e){return U(e)}})),C&&React.createElement(at.V,{style:{marginTop:35}},React.createElement("b",{style:{marginLeft:5,marginRight:15}},Be.SETTINGS.EXPERT_MODE),React.createElement(b.M,{disabled:r,onClick:function(){return It()}},Be.DASHBOARD.EXTRACT_REFERENCES),React.createElement(b.M,{className:"secondary",disabled:r,onClick:function(){return K("extractReferencesForPostId",!0)}},Be.DASHBOARD.EXTRACT_REFERENCES_FOR_POST_ID),React.createElement(b.M,{className:"danger",disabled:r,onClick:function(){return wt()}},Be.DASHBOARD.RESET_REFERENCES),React.createElement("span",null,"|"),React.createElement(b.M,{disabled:r,onClick:function(){return At()}},Be.DASHBOARD.MATCH_WITH_REFERENCES),"files"===d&&React.createElement("div",{style:{display:"flex",alignItems:"center",gap:"4px"}},React.createElement(h.s,null,(0,u.FE)((0,u.g7)(Be.DASHBOARD.FROM_FOLDER,xe))),React.createElement(b.M,{disabled:r,onClick:function(){return K("pickFolder",!0)}},Be.DASHBOARD.PICK_FOLDER)),React.createElement(b.M,{className:"danger",disabled:r,onClick:function(){return _t()}},Be.DASHBOARD.RESET_ISSUES))),React.createElement(ve.n,{isOpen:N.deleteAll,onRequestClose:function(){return K("deleteAll",!1)},title:Be.DASHBOARD.DELETE_ALL,content:(0,u.FE)((0,u.g7)(Be.DASHBOARD.DELETE_ALL_CONTENT,i,i<2?"":"s")),okButton:{onClick:function(){K("deleteAll",!1),We(null,!1,l)}},onCancel:{onClick:function(){K("deleteAll",!1)}}}),React.createElement(ve.n,{isOpen:N.showOriginalMedia,onRequestClose:function(){return K("showOriginalMedia",!1),void setTimeout((function(){return we(null)}),500)},title:"",content:React.createElement("div",{style:{display:"flex",justifyContent:"center"}},React.createElement("img",{src:_e,style:{maxWidth:"100%"}}))}),React.createElement(ve.n,{isOpen:N.trashAll,onRequestClose:function(){return K("trashAll",!1)},title:Be.DASHBOARD.DELETE_ALL,content:(0,u.FE)(Be.DASHBOARD.TRASH_ALL_CONTENT),okButton:{onClick:function(){K("trashAll",!1),We(null,!0)}},cancelButton:{onClick:function(){K("trashAll",!1)}}}),React.createElement(ve.n,{isOpen:N.forceTrashAll,onRequestClose:function(){return K("forceTrashAll",!1)},title:Be.DASHBOARD.DELETE_ALL,content:(0,u.FE)(Be.DASHBOARD.FORCE_TRASH_ALL_CONTENT),okButton:{onClick:function(){K("forceTrashAll",!1),ze()}},cancelButton:{onClick:function(){K("forceTrashAll",!1)}}}),React.createElement(ve.n,{isOpen:N.recoverAll,onRequestClose:function(){return K("recoverAll",!1)},title:Be.DASHBOARD.RECOVER_ALL,content:Be.DASHBOARD.RECOVER_ALL_CONTENT,okButton:{onClick:function(){K("recoverAll",!1),qe()}},cancelButton:{onClick:function(){K("recoverAll",!1)}}}),React.createElement(ve.n,{isOpen:fe.error,onRequestClose:Qe,title:React.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"}},React.createElement("span",null,Be.DASHBOARD.ERROR),React.createElement("span",{style:{fontSize:12,color:"#007bba",alignContent:"center",display:"flex",alignItems:"center",gap:15}},"Auto Retry In : ",React.createElement(ft.H,{initialTime:20,onEndCountdown:function(){fe.autoRetry(10)}}))),content:React.createElement(React.Fragment,null,React.createElement("b",null,fe.error?fe.error.message:"N/A"),React.createElement("p",null)),customButtons:React.createElement("div",{style:{display:"flex",width:"100%",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement(b.M,{className:"primary",onClick:function(){fe.retry()}},Be.DASHBOARD.RETRY),React.createElement("small",{style:{marginLeft:5}},React.createElement("a",{href:"#",onClick:function(){fe.autoRetry(10)}},Be.DASHBOARD.AUTO_RETRY)),React.createElement("div",{style:{flex:"auto"}}),React.createElement(b.M,{className:"secondary",onClick:function(){fe.resume()}},Be.DASHBOARD.SKIP),React.createElement("small",{style:{marginLeft:5,marginRight:10}},React.createElement("a",{href:"#",onClick:function(){fe.setAlwaysSkip(),fe.resume()}},Be.DASHBOARD.ALWAYS_SKIP)),React.createElement(b.M,{className:"danger",onClick:Qe},Be.DASHBOARD.STOP)),React.createElement("small",{style:{marginTop:10,lineHeight:"13px"}},Be.DASHBOARD.AUTO_RETRY_DESCRIPTION))}),React.createElement(ve.n,{isOpen:ae,title:Be.DASHBOARD.FINISHED,content:ae,okButton:{label:Be.DASHBOARD.CLOSE,onClick:function(){oe(""),fe.reset()}}}),React.createElement(ve.n,{isOpen:Ee,title:Be.DASHBOARD.RE_ATTACH_MEDIA,content:(0,u.FE)(Be.DASHBOARD.AUTO_ATTACH_FEATURE_MESSAGE),okButton:{label:Be.DASHBOARD.CLOSE,onClick:function(){ye(!1)}}}),N.pickFolders&&React.createElement(React.Fragment,null,React.createElement(ve.n,{isOpen:N.pickFolders,onRequestClose:function(){return K("pickFolders",!1)},title:Be.DASHBOARD.PICK,okOnEnter:!0,content:React.createElement("div",{style:{maxHeight:400,overflowY:"auto"}},React.createElement(mt.M,null,Ae.hierarchy.map((function(e){return function e(t,r,n){var a="/".concat(r.join("/")),o=Ae.root.replace(/\\/g,"/");console.log("🟢 normalizedRoot:",o),a=(a=a.replace(new RegExp("^".concat(o,"/"),"g"),"")).replace(/\\/g,"/");var i=function(){Ce(a)},c=t.name===n[0],l=c?n.slice(1):n,s=ke===a;return t.children.length?React.createElement(mt.R,{title:t.name,onClick:i,isExpanded:c,selected:s},t.children.map((function(t){return e(t,[].concat(mr(r),[t.name]),l)}))):React.createElement(mt.R,{title:t.name,onClick:i,selected:s})}(e,[e.name],xe.split("/").filter((function(e){return!!e})))})))),okButton:{onClick:function(){K("pickFolders",!1)}},cancelButton:{label:Be.DASHBOARD.RESET_FOLDERS,onClick:function(){Ce(null),K("pickFolders",!1)}}}),React.createElement(ve.n,{isOpen:N.pickFolder,onRequestClose:function(){return K("pickFolder",!1)},title:Be.DASHBOARD.PICK_FOLDER,okOnEnter:!0,content:React.createElement(mt.M,null,Ae.hierarchy.map((function(e){return function e(t,r,n){var a=t.name===n[0],o=a?n.slice(1):n,i="/".concat(r.join("/")),c=xe===i,l=function(){return De(i)};return t.children.length?React.createElement(mt.R,{title:t.name,onClick:l,isExpanded:a,selected:c},t.children.map((function(t){return e(t,[].concat(mr(r),[t.name]),o)}))):React.createElement(mt.R,{title:t.name,onClick:l,selected:c})}(e,[e.name],xe.split("/").filter((function(e){return!!e})))}))),okButton:{onClick:function(){K("pickFolder",!1)}},cancelButton:{label:Be.DASHBOARD.RESET_FOLDER,onClick:function(){De(Ae.root),K("pickFolder",!1)}}})),React.createElement(ve.n,{isOpen:N.extractReferencesForPostId,onRequestClose:function(){return K("extractReferencesForPostId",!1)},title:Be.DASHBOARD.EXTRACT_REFERENCES_FOR_POST_ID,okOnEnter:!0,content:React.createElement(ge.A,{name:"extract-references-for-post-id",value:Me,onChange:function(e){return Fe(e)}}),okButton:{onClick:function(){It(Me),K("extractReferencesForPostId",!1),Fe(null)}},cancelButton:{onClick:function(){K("extractReferencesForPostId",!1)}}})))};var Or=wp.element.render,Nr=new l.E({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnMount:!1,retry:!1,placeholderData:function(e){return e}}}}),xr={fetcher:u.m9,revalidateOnFocus:!1};document.addEventListener("DOMContentLoaded",(function(e){var t=document.getElementById("wpmc-admin-settings");t&&Or(React.createElement(gt.BE,{value:xr},React.createElement(f.A,null,React.createElement($e,null))),t),(t=document.getElementById("wpmc-dashboard"))&&Or(React.createElement(gt.BE,{value:xr},React.createElement(rr,null,React.createElement(f.A,null,React.createElement(Ar,null)))),t);var r=document.getElementById("meow-common-dashboard");r&&Or(React.createElement(s.Ht,{client:Nr},React.createElement(f.A,null,React.createElement(Re,null))),r)}))},1594:e=>{e.exports=React},5206:e=>{e.exports=ReactDOM}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var o=r[e]={exports:{}};return t[e](o,o.exports,n),o.exports}n.m=t,n.amdO={},e=[],n.O=(t,r,a,o)=>{if(!r){var i=1/0;for(u=0;u<e.length;u++){for(var[r,a,o]=e[u],c=!0,l=0;l<r.length;l++)(!1&o||i>=o)&&Object.keys(n.O).every((e=>n.O[e](r[l])))?r.splice(l--,1):(c=!1,o<i&&(i=o));if(c){e.splice(u--,1);var s=a();void 0!==s&&(t=s)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[r,a,o]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={57:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var a,o,[i,c,l]=r,s=0;if(i.some((t=>0!==e[t]))){for(a in c)n.o(c,a)&&(n.m[a]=c[a]);if(l)var u=l(n)}for(t&&t(r);s<i.length;s++)o=i[s],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(u)},r=self.wpJsonWpmc=self.wpJsonWpmc||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0;var a=n.O(void 0,[121],(()=>n(9371)));a=n.O(a)})();
//# sourceMappingURL=index.js.map